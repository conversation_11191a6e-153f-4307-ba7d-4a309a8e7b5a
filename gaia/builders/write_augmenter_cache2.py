#  usage:  python -m gaia.builders.write_augmenter cache -b 250 -g agbase

#  adjacent importing
import os
import sys
import click
import sqlite3
#  add modules to python path for adjacent importing (python 2.7 way)
parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)

#  es connect
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico

from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_fs.paths import gfs_folder_local

#  local
from gaia.builders import augmenters
from gaia.builders.builders import AgsearchBuilder
from gaia.builders.decorators import timethis

#  gaiafs root
#  TODO: tree, subfolder
CACHE_PATH = gfs_folder_local(tree='gfs_datasets')
if 1:
    CACHE_PATH = os.path.join(CACHE_PATH, 'build')
print('CACHE PATH', CACHE_PATH)


#  records *source*
SRC_ALIAS = 'opensearch_worker_fast'
#SRC_IDX = 'idx_cb10_fresh1'
SRC_IDX = 'idx_agsearch_worker_fast1'


#  get live index - most recent
elastico = GaiaElastico()
elastico.connect(alias=SRC_ALIAS)
LATEST_IDX = list(elastico.es.indices.get('agsearch_cb'))[0]
print(f'LATEST_IDX: {LATEST_IDX}')


#  augmenetrs cache for groups
spiders = (
    #augmenters.Agspider,
    #augmenters.Babyspider, # don't need to write - hasn't been updated
    augmenters.BabyspiderPlaintext,
    #augmenters.LangDetect,
)

agbase = (
    augmenters.Xform,
    #augmenters.Investor,
    augmenters.People,
    #augmenters.Rounds,
    augmenters.Agbase,
)

investors = (
    augmenters.Investor,
)

ml = (
    augmenters.Dealer,
    #augmenters.Knn,
)

mlorg = (
    augmenters.Mlorg,
)

subs = (
    augmenters.SubscribersByDomain,
)

babyspider = (
    augmenters.Babyspider,
)


sbert = (
    augmenters.SBert_EMB02xlm,
)

#@timethis
def get_batch(builder=None, cb_res=None, batch_amt=None):
    batch = builder.get_batched(generator=cb_res, batch_amt=batch_amt)
    # cached, uuids_homepage_dict, uuids_doc_dict
    data_dict = builder.es_unpack(
        batch=batch,
        dest_idx=builder.dest_idx
    )
    #  {'cached', Dict, 'uuids_doc_dict': Dict, 'uuids_homepage_dict': Dict}
    return data_dict


def get_batchTEMP(builder=None, cb_res=None, batch_amt=None):
    batch = builder.get_batched(generator=cb_res, batch_amt=batch_amt)
    return batch


#@timethis
def process_batch(augmenter=None, kv=None, data_dict=None):
    # TODO: envelopize data

    computed_dict = augmenter.compute(data_dict=data_dict)
    #  key/ value store write cache
    print('writing {} cache entries for {}'.format(len(computed_dict.keys()), augmenter.formatted_name))
    # TODO:
    if 0:
        print(f'skipping builder cache for {augmenter.formatted_name}')
    else:
        for key, value in computed_dict.items():
            #print(f'DEBug!!!!set cache key: {key}')
            kv.set(key, value)


def process_batchTEMP(augmenter=None, kv=None, data_dict=None):
    # TODO: envelopize data

    #computed_dict = augmenter.compute(data_dict=data_dict)
    computed_dict = {}
    for row in data_dict:
        if row['_source'].get('plaintext'):
            computed_dict[row['_source']['uuid']] = row['_source']['plaintext']
    #  key/ value store write cache
    print('writing {} cache entries for {}'.format(len(computed_dict.keys()), augmenter.formatted_name))
    # TODO:
    if 0:
        print(f'skipping builder cache for {augmenter.formatted_name}')
    else:
        for key, value in computed_dict.items():
            #print(f'DEBug!!!!set cache key: {key}, kv: {kv.file_path}')
            kv.set(key, value)




def update_augmenter_path(augmenter):
    src_path = CACHE_PATH
    DEST_CACHE_PATH = gfs_folder_local(tree='gfs_datasets')
    dest_path = os.path.join(DEST_CACHE_PATH, f'{augmenter.formatted_name}.db')
    if os.path.getsize(dest_path) > os.path.getsize(src_path):
        print('moving to dest path...', dest_path)
        #shutil.move(src_path, dest_path)
    else:
        print('src_path is smaller than dest_path so will not move!')


@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-g', '--group', type=str, default=None)
@click.option('-f', '--full_update', is_flag=True, default=False)
#@timethis
def run(group=None, batch_amt=None, hard_limit=None, full_update=False):

    #  convert str arg to variable
    cache_for = globals()[group]

    # used for get bacth
    builder = AgsearchBuilder()

    if full_update:
        print('write cache, full update...')
        #  get source records
        #  Full scan
        cb_res, _ = elastico.docs_scan(
            index=SRC_IDX,
            pp_count=batch_amt
        )

    #  initialize count
    HARD_COUNT = 0
    #CONTINUE_FROM = 1480000
    CONTINUE_FROM = None

    #  holds keyvalue instancesct
    kv_dict = {}
    augmenter_instances = []

    for augmenter_cls in cache_for:
        #  create augmenter instances
        augmenter = augmenter_cls()
        augmenter_instances.append(augmenter)

        #  dict of KV instances specific for augmenters (filepath)\
        file_path = os.path.join(CACHE_PATH, f'{augmenter.formatted_name}.db')
        kv = KeyValueStore_sqlite3(file_path=file_path)
        kv_dict[augmenter.formatted_name] = kv

        #  reset kv if full_update
        if full_update and CONTINUE_FROM is None:
            print('reset kv... ', kv.file_path)
            kv.reset()

    pp_from = 0
    size = batch_amt

    #  loop through generator results
    while True:

        if not full_update:
            #  TODO: make this dynamic
            #  only since latest date
            print(f'partial update only... Source: {SRC_IDX}, LATEST: {LATEST_IDX}')

            latest = elastico.latest_record(index=LATEST_IDX)

            cb_res = elastico.after_date(
                index=SRC_IDX,
                date=latest['date'], # datetime
                pp_from=pp_from,
                pp_count=size
            )
            data_dict = get_batch(
                builder=builder,
                cb_res=cb_res,
                batch_amt=batch_amt
            )

        else:
            data_dict = get_batchTEMP(
                builder=builder,
                cb_res=cb_res,
                batch_amt=batch_amt
            )
            #print('data dict len', len(data_dict.keys()))
            #print(f'debug data dict: {list(data_dict.values())[0][:25]}')

            #  debug
            #if HARD_COUNT < 730750:
            #    print('hard count skipping...', HARD_COUNT)
            #    HARD_COUNT += batch_amt
            #    continue

        if CONTINUE_FROM and HARD_COUNT < CONTINUE_FROM:
            HARD_COUNT += batch_amt
            print('skipping... HARD COUNT: {}'.format(HARD_COUNT))
            continue

        print('process batch...')

        #  process augmenters
        for augmenter in augmenter_instances:
            try:
                print('augmenting batch...')
                process_batchTEMP(
                    augmenter=augmenter,
                    kv=kv_dict[augmenter.formatted_name],
                    data_dict=data_dict,
                )
            except Exception as e:
                print('prcess augmenters error', e)
                import traceback
                print(traceback.format_exc())
                import pdb; pdb.set_trace()
        #  break if all records done
        if len(data_dict) != batch_amt:
            print('cb_cache out. No more records!')
            for augmenter in augmenter_instances:
                update_augmenter_path(augmenter)
            break

        #  break if HARD_LIMIT reached
        HARD_COUNT += batch_amt
        print('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ HARD COUNT: {}'.format(HARD_COUNT))
        if hard_limit and HARD_COUNT >= hard_limit:
            print('HARD LIMIT REACHED')
            for augmenter in augmenter_instances:
                update_augmenter_path(augmenter)
            break

        #  increment ES offset
        pp_from += size


if __name__ == '__main__':
    run()
