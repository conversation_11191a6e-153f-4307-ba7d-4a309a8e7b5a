import os
import sys
import click
import time
import pandas as pd
from typing import List
from gaia.builders.omni_record_augmenter import CACHE_PATH, flatten_dict
from gaia.builders.batch_generators import batch_generator, parquet_generator
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.builders import augmenters_omni as augmenters
from gaia.builders.omni_builder_opensearch import OmniBuilderGoidOpensearch
from gaia.builders.omni_builder_parquet import OmniBuilderGoidParquet


def replace_nan(batch: List) -> List:
    filtered = []
    for rec in batch:
        d = {k:v if not pd.isnull(v) else None for k, v in rec.items()}
        filtered.append(d)
    return filtered





@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-s', '--src_path', type=str, default=None)
@click.option('-o', '--output', type=str, default=None)
def run(batch_amt: int, hard_limit: int, src_path: str, output: str) -> any:

    print(f'.................... run kwargs: {locals()}')

    #  parquet builder
    if output is not None and output.endswith(('.pq', '.parquet')):
        print('using OmniBuilderGoidParquet...')
        builder = OmniBuilderGoidParquet(dest_path=output)

    #  opensearch builder
    #  default if output not specified
    else:
        DEST_CLUSTER_ALIAS = 'opensearch_worker_fast'
        src_name = src_path.split('/')[-1].replace('.', '_')
        DEST_IDX = f'idx_omni3__{src_name}'
        elastico = GaiaElastico()
        elastico.connect(alias=DEST_CLUSTER_ALIAS)

        print('using OmniBuilderGoidOpensearch...')
        builder = OmniBuilderGoidOpensearch(elastico=elastico, idx=DEST_IDX, augmenters=[])
        print('augmenters', builder.get_augmenters())

    #  start from row
    continue_from = 0
    if not continue_from:
        builder.reset()

    #  source data (default)
    if not src_path:
        src_path = '/var/lib/gaia/GAIA_FS/frames/omni/org_merge/frame.parquet'

    #  src is parquet
    if src_path.endswith(('.pq', 'parquet')):
        src_gen = parquet_generator(path=src_path) #  batches of 1000 rows

    #  src is kv
    elif src_path.endswith('.db'):
        kv = KeyValueStore_sqlite3(file_path=src_path)
        src_gen = kv.iterator(key_only=False, as_dict=True)

    #  augment and store loop
    count = 0

    for batch in batch_generator(src=src_gen, batch_size=batch_amt, from_kv=True):

        batch = replace_nan(batch)

        #  flatten
        batch = [flatten_dict(b) for b in batch]

        #print('.................batch[0]', batch[0])
        if continue_from and count < continue_from:
            print('skipping...')
        else:
            #print('augment and cache batch...', batch[0])
            builder.augment_and_insert(batch)

        #  *** TERMINATE ***: end of source data
        if len(batch) < batch_amt:
            print('no more records.')
            break

        #  increment count
        count += len(batch)
        print(f'.............................................count: { count }')

        #  *** TERMINATE ***: hard_limit reached
        if hard_limit and count >= hard_limit:
            print('hard limit reached.')
            break

    builder.close()
    print('done!')

if __name__ == '__main__':
    run()

