import os
from typing import List
from gaia.builders.omni_record_augmenter import CACHE_PATH
from gaia.builders.omni_builder_goid import OmniBuilderGoid
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico


KV_CACHE_PATH = os.path.join(CACHE_PATH, 'kv')


class OmniBuilderGoidOpensearch(OmniBuilderGoid):

    def __init__(self, elastico: GaiaElastico, idx: str, augmenters: List = []):
        self.elastico = elastico
        self.idx = idx
        super().__init__(augmenter_cache_path=KV_CACHE_PATH, augmenters=augmenters)

    def _recreate_index(self):
        self.elastico.index_delete(self.idx)
        self.elastico.index_create_knn(index=self.idx)


    def _pre_index(self):
        self.elastico.es.indices.put_settings(
            index=self.idx,
            body={"refresh_interval": -1, "number_of_replicas": "0"},
            ignore_unavailable=True

        )
    def _post_index(self):
        self.elastico.es.indices.put_settings(
            index=self.idx,
            body={"refresh_interval": "30s", },
            ignore_unavailable=True
        )

    def reset(self):
        """Reset target idx and prepare idx settings for inserts."""
        self._recreate_index()
        self._pre_index()
        super().reset()

    def close(self):
        """Set target idx settings for production."""
        self._post_index()

    def recreate_index(self):
        self.elastico.index_delete(self.idx)
        self.elastico.index_create_knn(index=self.idx)

    def metadata_batch(self, batch: List) -> List:
        """Prepare record metadata for Opensearch idx."""
        updated_batch = []

        for _rec in batch:
            rec = {}
            rec['_type'] = '_doc'
            rec['_index'] = self.idx
            rec['_id'] = _rec['GOID']
            rec['_source'] = _rec
            #  Cast funding_total_usd to int
            #if rec["_source"].get("funding_total_usd") is not None:
            #    rec["_source"]["funding_total_usd"] = int(rec["_source"]["funding_total_usd"])
            updated_batch.append(rec)

        return updated_batch

    def augment_and_insert(self, batch: List) -> List:
        """Prepare batch of records for opensearch, augment with cached data and insert into Opensearch Index."""
        batch = self.metadata_batch(batch)
        augmented_batch = self.augment_batch(batch, record_key='_source')
        self.elastico.insert_bulk(bulks=batch, multi=True)

        return augmented_batch


    def insert(self, batch: List) -> List:
        """Prepare batch of records for opensearch, insert into Opensearch Index."""
        batch = self.metadata_batch(batch)
        self.elastico.insert_bulk(bulks=batch, multi=True)

        return batch

