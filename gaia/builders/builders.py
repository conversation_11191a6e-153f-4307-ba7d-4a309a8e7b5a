import multiprocessing
import time
import learn.sbert.sbert_flask_client

from gaia.builders import augmenters
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.builders.decorators import timethis
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_fs.paths import gfs_folder_local
from gaia.gaia_ml.family.fam_hf.sbert import sbert_base

"""
Record Builders Module.

Base Class Builders can be used as Mixins for Higher Level Builders.

Base Class Builders should not implement contructor args, to avoid
MRO issues. ref: https://www.python.org/download/releases/2.2.3/descrintro/#mro

Base Class Builders should implement the fewest number of methods possible. Highly focussed.
"""


class Builder(object):
    """
    Base Class Builder, L0.
    """

    def __init__(self):
        pass

    @staticmethod
    def multiprocess_queue_worker(num=None, es_conn=None, queue=None, callback=None):
        """
        Gets records from a multiprocessing queue
        and calls callback with queue result.
        """

        print('worker starting...', num)
        tries = 1

        print('worker inital sleep...', num)
        time.sleep(60)

        while True:
            print(f'{num}..............................................................Queue size: {queue.qsize()}')
            obj = None
            try:
                obj = queue.get(block=True, timeout=5.0)
                if obj == 'DIE CMD':
                    print('DIE CMD found, terminating worker...')
                    return
                else:
                    tries = 1
                    pass
            except Exception as e:
                print('multiprocess_queue_worker. error: {}. Try {} of 3'.format(type(e), tries))
                if tries <= 3:
                    tries += 1
                    print(f'{num} worker sleeping for {30*tries} secs...')
                    time.sleep(30 * tries)
                    continue
                return

            if not obj:
                print('multiprocess_queue_worker: queue empty')
                continue

            print(f'{num} Insert worker: bulk_insert....')

            #  call callback function
            try:
                callback(es_conn, obj)
            except Exception as e:
                raise


class ElasticsearchMixin(object):
    """
    Base Class Builder. ElasticSearch.
    """

    def __init__(self):
        pass

    @staticmethod
    def clean_ml_fields(doc_array=None):
        """
        Cleans ML prediction fields for ES insertion.
        """

        #  debugging
        missing = {}
        missing['agbase'] = 0
        missing['ml_data_web'] = 0

        cleaned = []

        for doc in doc_array:
            try:
                ml_data_web = doc['_source']['agbase']['ml_data_web']
            except Exception as e:
                ml_data_web = None
                #missing[e.args[0]] += 1

            if ml_data_web:
                #  TAG LIKELY
                tag_likely_dict = {}
                for tag in ml_data_web['tag_likely']:
                    tag_likely_dict[tag[0]] = tag[1]
                doc['_source']['agbase']['ml_data_web']['tag_likely'] = tag_likely_dict

                #  CAT LIKELY
                cat_likely_dict = {ml_data_web['cat_likely'][0]: ml_data_web['cat_likely'][0]}
                doc['_source']['agbase']['ml_data_web']['cat_likely'] = cat_likely_dict

            cleaned.append(doc)

        return cleaned

    def get_batched(self, generator=None, batch_amt=None):
        batch = []
        count = 0
        for rec in generator:
            batch.append(rec)
            #  break when batch_amt reached
            count += 1
            if count >= batch_amt:
                break
        #  return a list
        return batch

    def es_unpack(self, batch=None, dest_idx=None):
        """
        Returns a dict of modified ES results.
        """
        #  input: elasticsearch records set
        #  output from self.scan_es

        print('es unpack...')

        uuids_homepage_dict = {}
        uuids_doc_dict = {}
        cached = []

        for rec in batch:
            #  basics
            rec['_type'] = '_doc'
            rec['_index'] = dest_idx

            uuid = rec["_id"]

            #if rec["_source"].get("homepage_url"):
            uuids_homepage_dict[uuid] = rec["_source"].get("homepage_url")

            doc = str(rec["_source"].get("company_name") or '') + " " + \
                  str(rec["_source"].get("short_description") or '') + " " + \
                  str(rec["_source"].get("description") or '')

            #  Cast funding_total_usd to int
            if rec["_source"].get("funding_total_usd") is not None:
                rec["_source"]["funding_total_usd"] = int(rec["_source"]["funding_total_usd"])

            uuids_doc_dict[uuid] = doc

            cached.append(rec)

        return {'cached': cached, 'uuids_homepage_dict': uuids_homepage_dict, 'uuids_doc_dict': uuids_doc_dict}

    def scan_es(self, es_conn=None, idx=None, batch_amt=None):
        print('ElasticsearchMixin: scan_es. {}. Batch amt: {}'.format(idx, batch_amt))
        cb_res, query = es_conn.docs_scan(
            index=idx,
            pp_count=batch_amt
        )
        return cb_res

    @staticmethod
    def recreate_index(es_conn=None, idx=None, use_knn=True):
        """
        Destroy and Create index.
        """
        print('fn: recreating idx... {}'.format(idx))
        #  delete idx
        es_conn.index_delete(idx)
        #  create idx
        if use_knn:
            es_conn.index_create_knn(index=idx)
        else:
            es_conn.index_create(idx)

    @staticmethod
    def reset_idx_options(es_conn=None, idx=None):
        es_conn.es.indices.put_settings(
            index=idx,
            body={"refresh_interval": "30s", },
            ignore_unavailable=True
        )


class AugmenterMixin(object):

    def __init__(self):
        pass

    @staticmethod
    def get_augmenters():
        augmenters_map = {
            'dealer': augmenters.Dealer,
            'investors': augmenters.Investor,
            'rounds': augmenters.Rounds,
            'agbase': augmenters.Agbase,
            'mlorg_text': augmenters.Mlorg,
            'xform_preds': augmenters.Xform,
            'knn': augmenters.Knn,
            #'knn_EMB_02_xlm': augmenters.SBert_EMB02xlm,
            #  web page augmenters
            'babyspider': augmenters.Babyspider,
            'webtext': augmenters.Agspider,
            'plaintext': augmenters.BabyspiderPlaintext,
            'langdetectweb': augmenters.LangDetectWeb,
            'langdetectsummary': augmenters.LangDetectSummary,
            'subscribers': augmenters.SubscribersByDomain,
        }
        return augmenters_map


    @staticmethod
    def babyspider_fix_encoding(res=None):
        print('fix babyspider encoding...')
        for k, v in res.items():
            if v is None:
                continue
            parse = v.get('parse')
            if parse is None:
                continue
            pt = parse.get('plaintext')
            if pt is None:
                continue
            try:
               pt.encode()
            except Exception as e:
               print('babyspider - bad unicode, fixing...')
               res[k]['parse']['plaintext'] = pt.encode('utf-8', 'ignore').decode()
        return res


    def transform(self, iterable=None, augment=True, uuids_doc_dict=None):
        """
        Transforms cb records.
        """

        #  prep to augment records
        uuid_list = uuids_doc_dict.keys()
        augmenter_results_dict = {}

        #  create dict of results for each augmenter
        if augment:
            print('iterate augmenters...')
            for key, cls in self.get_augmenters().items():

                #  instantiate augmenter class
                augmenter = cls()
                #  maintain a single KeyValue instance per augmenter
                kv_dict = {}

                count = 1
                while count <= 3:

                    try:
                        #  dict of KV instances specific for augmenters (filepath)
                        try:
                            kv = kv_dict[augmenter.formatted_name]
                        except KeyError:
                            #  get db source path
                            cache_path = gfs_folder_local(tree='gfs_datasets', sub_folder='')
                            kv = KeyValueStore_sqlite3(file_path=cache_path + '{}.db'.format(augmenter.formatted_name))
                            kv_dict[augmenter.formatted_name] = kv
                            #print('AugmenterMixin - get from cache {}'.format(cache_path + '{}.db'.format(augmenter.formatted_name)))
                            #print(f'uuid keys: {list(uuids_doc_dict.keys())[0]}')

                        print(f'get from augmenter kv: {augmenter.formatted_name}')
                        res = kv.get_mult(uuids_doc_dict.keys())

                        if key == 'babyspider' and res is not None:
                            res = self.babyspider_fix_encoding(res)
                        #  key is augmenter label
                        if key == 'mlorg' and res is not None: print('mlorg key~~~~~~~~~~~~~~~~~~~~~~'); import pdb; pdb.set_trace()
                        if res and len(res.keys()):
                            augmenter_results_dict[key] = res

                    except Exception as e:
                        print('Augmenter Error: {}. Sleeping...'.format(e))
                        time.sleep(60*count)
                        count += 1

                    #  break if successful
                    break

                #  too many errors
                if count > 3:
                    raise Exception('Augmenter Error: {}.'.format(e))

        #  loop through results batch and
        #  augment with key as ES field name
        print('create update_doc_array...')

        update_doc_array = []

        for rec in iterable:
            uuid = rec["_id"]

            if augment:
                #  cycle through augmenter dicts
                for key in augmenter_results_dict.keys():

                    if uuid in augmenter_results_dict[key]:
                        rec["_source"][key] = augmenter_results_dict[key][uuid]

            #  TODO: Envelopize?

            #  concat company_name, description and webtext
            try:
                company_name = rec['_source'].get('company_name') or ''
                description = rec['_source'].get('description') or rec['_source'].get('short_description')
                description = description or ''
                plaintext = rec['_source'].get('plaintext') or ''
                rec['_source']['summary_text'] = ' '.join([
                    company_name,
                    description,
                    plaintext
                ])
            except Exception as e:
                import pdb; pdb.set_trace()

            update_doc_array.append(rec)

        #  cleanup ml fields
        print('clean ml...')
        update_doc_array = self.clean_ml_fields(
            doc_array=update_doc_array)

        print('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~  update_doc_array length:', len(update_doc_array))
        return update_doc_array


class AgsearchBuilder(Builder, ElasticsearchMixin, AugmenterMixin):
    """
    Builds an ES Index from an iterable of records.
    """

    def __init__(self, dest_idx=None, es_alias=None, status_log_path=None):
        self.dest_idx = dest_idx
        self.es_alias = es_alias
        self.status_log_path = '/var/log/builder/agsearch_status.log'

    def _save_status(self, uuid=''):
        print('save status...{}'.format(uuid))
        try:
            with open(self.status_log_path, 'w') as f:
                f.write(uuid)
        except Exception as e:
            print('error: _save_status', e)
            return False
        return True

    def _load_status(self):
        try:
            with open(self.status_log_path, 'r') as f:
                uuid = f.read()
        except Exception as e:
            print('error: _load_status', e)
            return None
        if uuid == '':
            return None
        return uuid.replace('\n', '')

    def _close_status(self):
        print('closing status...')
        # TODO: does this write empty string?
        self._save_status()
        #open(self.status_log_path, 'w').close()

    def insert_bulk(self, es_conn=None, records=None):
        es_conn.insert_bulk(bulks=records, multi=True)


    def run(self, iterable=None, multiprocess=False, batch_amt=None, HARD_LIMIT=None, full_update=False, es_conn=None, src_kv=None):
        #  Connect to ElasticSearch
        if not es_conn:
            print('creating new ES connection...')
            es_conn = GaiaElastico()
            es_conn.connect(alias=self.es_alias)
        else:
            print('re-using ES connection...')

        #  None if not interrupted
        continue_from = self._load_status()

        # TODO
        #continue_from = False
        if not continue_from and full_update:
            print('continue from is Empty, recreating index.', self.dest_idx)
            #import pdb; pdb.set_trace()
            self.recreate_index(es_conn=es_conn, idx=self.dest_idx, use_knn=True)

        if continue_from:
            print('Continue from =====> {}'.format(continue_from))

        #  for performance
        try:
            es_conn.es.indices.put_settings(
                index=self.dest_idx,
                body={"refresh_interval": -1, "number_of_replicas": "0"},
                ignore_unavailable=True
            )
        except Exception as e:
            print('builder: elasticsearch settings error, recreating index...', self.dest_idx)
            # TODO:  for some reason we end up here if there is an exisiting index and we can still insert...

            #self.recreate_index(es_conn=es_conn, idx=self.dest_idx, use_knn=True)
            #  if we recreate index, we must reset continue from
            #self._save_status(uuid='')
            #continue_from = None
            print('try anyway??...')

        if multiprocess:
            #  num processes depends on cpu cores
            N = multiprocessing.cpu_count() - 1 or 1
            N = 1
            print(f'creating {N} workers....')

            #  create a queue object
            queue = multiprocessing.Queue()
            workers = []

            callback = self.insert_bulk

            for i in range(N):

                #  workers which process items from queue
                insert_worker = multiprocessing.Process(
                    target=self.multiprocess_queue_worker,
                    args=(i+1, es_conn, queue, callback))
                #insert_worker.daemon = True

                #  start all workers
                insert_worker.start()

                #  keep reference to all workers
                workers.append(insert_worker)

        HARD_COUNT = 0
        while True:

            #  batch in memory
            try:
                print('get batch...')
                if src_kv:
                    print('get batch from src_kv...')
                    batch = next(src_kv.get_batch_generator(batch_size=batch_amt), None)
                else:
                    batch = self.get_batched(generator=iterable, batch_amt=batch_amt)
                print(f'batch len is: {len(batch)}')
            except Exception as e:
                print('GET BATCHED ERROR!')
                print('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ AgsearchBuilder.get_batched error: {}'.format(str(e)))
                raise
                #print('sleeping for 60s...')
                #time.sleep(60)
                #print('get_batched try again...')
                #batch = self.get_batched(generator=iterable, batch_amt=batch_amt)

            #  we can continue a batch if borked...
            if continue_from:
                if any(continue_from in d['_id'] for d in batch):
                    print('continue from this batch!', HARD_COUNT)
                    #  reset continue_from and proceed in current loop
                    continue_from = None
                else:
                    #  skip until uuid found in batch
                    print('skip this batch...', HARD_COUNT)
                    HARD_COUNT += batch_amt
                    continue

            #  ElasticsearchBuilder.UNPACK
            #  update batch with es attrs, returned batch in memory
            '''
            batch, uuids_homepage_dict, uuids_doc_dict = self.es_unpack(
                batch=batch, dest_idx=self.dest_idx)
            '''


            data_dict = self.es_unpack(batch=batch, dest_idx=self.dest_idx)
            #  TODO: split uuids into chunks, augment in parallel
            '''
            update_doc_array = []
            tasks = []
            for group in chunker(uuids_doc_dict.keys(), batch_amt/4):
               res = pool.apply_async(transform, args=(idx, group, self.cb_cache)).get()
               update_doc_array.extend(res)
            '''

            update_doc_array = self.transform(
                iterable=data_dict['cached'],
                uuids_doc_dict=data_dict['uuids_doc_dict'],
                augment=True
            )
            #import pdb; pdb.set_trace()
            if multiprocess:
                #  put records on queue if queue size < 10, else pause
                if queue.qsize() >= 10:
                    print('queue filling up. Pausing for 60s')
                    time.sleep(60)
                print(f'put on queue... qsize: {queue.qsize()}')
                queue.put(update_doc_array)

            #  single process with or with out parallel_bulk
            #  ! use multi-threaded to avoid too large request error
            else:
                es_conn.insert_bulk(bulks=update_doc_array, multi=True)


            #  save status after records added to queue
            #  write first record to status log
            #  only if not in continue_from state
            if not continue_from:
                try:
                    pass
                    #  do this in worker
                    #self._save_status(uuid=batch[0]['_id'])
                except IndexError as e:
                    print('self._save_status IndexError.', e)

            #  exit loop when cb_cache is less than batch_amt
            #  can be overridden by HARD_LIMIT
            if len(batch) != batch_amt:
                print('=======================> NO MORE RECORDS ! <=======================')
                print(f'len(batch) = {len(batch)},  batch_amt = {batch_amt}')
                if multiprocess and queue:
                    print('cb_cache out: put die command...')
                    for _ in range(N):
                        queue.put('DIE CMD')
                    queue.close()
                    queue.join_thread()
                    insert_worker.join()
                break

            #  exit when a defined HARD LIMIT reached
            #  useful for debugging with a subset of records
            HARD_COUNT += batch_amt
            print('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ HARD COUNT: {}'.format(HARD_COUNT))
            if HARD_LIMIT and HARD_COUNT >= HARD_LIMIT:
                if multiprocess and queue:
                    for _ in range(N):
                        print('put die command...')
                        queue.put('DIE CMD')
                    print('closing queue...')
                    queue.close()
                    queue.join_thread()
                break

        if multiprocess:
            #  wait for workers to finish
            for w in workers:
                w.join()

        #  after all records inserted
        self.reset_idx_options(es_conn=es_conn, idx=self.dest_idx)
        #  empty status log file
        #  TODO: taken care of by another process
        #self._close_status()
