#  usage:  python -m gaia.builders.omni_record_augmenter -b 1 -g ml.xform -o 0 -l 1
import os
import click
import time
import multiprocessing
from multiprocessing import Queue
from queue import Empty
from typing import List
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.gaia_fs.paths import gfs_folder_local
from gaia.builders import augmenters_omni as augmenters
from gaia.builders import augmenters_vendors
from gaia.builders import augmenters_web
from gaia.builders.batch_generators import csv_generator, parquet_generator, batch_generator, parquet_generator_slice
from gaia.core.gaia_goid import build_goid
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.util.formats.frame_incremental import PolarsParquetWriter, PandasParquetWriter, PandasCSVWriter
from gaia.builders.kv_to_pq import kv2pq


#  globals
REFRESH_AUGMENTER_CACHE = False


def curried_constructor(cls, **kwargs):
    def constructor():
        return cls(**kwargs)
    return constructor


#  Augmenter Groups  #
spiders = (
    #augmenters.Agspider,
    #augmenters.Babyspider, # don't need to write - hasn't been updated
    #augmenters.BabyspiderPlaintext,
    augmenters.LangDetectWeb,
    augmenters.LangDetectSummary,
)
agbase = (
    augmenters.Dealer,
    augmenters.Investor,
    augmenters.People,
    augmenters.Rounds,
    augmenters.Agbase,
)
ml = (
    augmenters.Xform,
    augmenters.Knn,
    augmenters.SBert_EMB02xlm,
)
mlorg = (
    augmenters.Mlorg,
)
geo = (
    augmenters.GeoRegion,
)
vendors = (
    augmenters_vendors.AugmenterClerkFetchAndParseSingle,
)
vendors_multi = (
    #curried_constructor(augmenters_vendors.AugmenterClerkFetchAndParseMulti, transform='describe_parse'), # TESTING ONLY, N = 1

    #curried_constructor(augmenters_vendors.AugmenterClerkFetchAndParseMulti, transform='describe_niumscore'), #  N=50
    curried_constructor(augmenters_vendors.AugmenterClerkFetchAndParseMulti, transform='describe_theme'), #  N=20
    #curried_constructor(augmenters_vendors.AugmenterClerkFetchAndParseMulti, transform='describe_parse_simple_compact'), #  N=17
)
babyfetch = (
    augmenters_web.FetchAndParsePlainText,
)


def get_augmenters_by_group(group: str) -> List:
    """Return list of augmenters by group."""
    if '.' in group:
        subgroup = group.split('.')[1]
        augmenters = globals()[group.split('.')[0]]
        augmenters = [a for a in augmenters if a().formatted_name == subgroup]
    else:
        #  convert str arg to variable
        augmenters = globals()[group]
    return augmenters


def concat_description(row):
    """Concatenate description, keywords fields."""
    #  default is name only
    if row.get("keywords") is None:
        pass

    if row.get("short_description") is None and row.get("description") is None and row.get("keywords") is None:
        return str(row["name"])

    doc = str(row.get("name") or '') + "\n" + \
          str(row.get("short_description") or '') + "\n" + \
          str(row.get("description") or '') + "\n " + \
          str(row.get("keywords") or '')

    return doc.strip()


def get_pq_writer(dir_path: str) -> PandasParquetWriter:
    print(f'\n*** parquet file path: {dir_path}\n')
    #return PandasParquetWriter(folder=dir_path, N=20)
    return PandasParquetWriter(folder=dir_path, N=100000)


def get_csv_writer(dir_path: str) -> PandasCSVWriter:
    return PandasCSVWriter(folder=dir_path, N=20000)


def get_kv_writer(file_path: str) -> KeyValueStore_sqlite3:
    print(f'\n*** kv file path: {file_path}\n')
    kv = KeyValueStore_sqlite3(file_path=file_path)
    kv.set_mode(mode="unsafe_cheetah", cursor=None)
    return kv


def omni_augmenter_worker(queue, augmenters, worker_id):
    """Worker for processing (augmenting) batrch of records."""

    print('worker launched: ', worker_id)

    omni_augmenter = OmniRecordAugmenter(augmenters=augmenters, worker_id=worker_id)

    EMPTY_COUNT_LIMIT = 3
    empty_counts = 0

    #  wait for queue to fill?
    time.sleep(1)

    while True:
        try:
            batch = queue.get(block=False, timeout=60)
            if batch == 'DIE':
                break
            #  process batch
            omni_augmenter.process_batch(batch)

        except Empty:
            print("Empty Exception queue : Empty")
            empty_counts +=1

        except Exception as e:
            print("[Exception] omni_queue_worker",e)
            raise

        if empty_counts >= EMPTY_COUNT_LIMIT:
            return

    print(f'WORKER {worker_id} DONE!')
    return True


def launch_omni_augmenter_workers(augmenters: str):
    max_procs = 8
    workers = []
    queue = Queue(maxsize=10)
    for i in range(0, max_procs):
        print('launching worker with id: ', i+1, '...')
        write_proc = multiprocessing.Process(
            target=omni_augmenter_worker, args=(queue, augmenters, i+1))
        write_proc.start()
        workers.append(write_proc)
    return queue, workers


class OmniRecordAugmenter():
    """OmniRecordAugmenter class for processing batches of records."""

    set_opensearch = False
    augmenters_config = {}

    def __init__(self, augmenters: List, worker_id: int = 0):
         self.worker_id = worker_id
         self._configure_augmenters(augmenters)

         if self.set_opensearch:
             self.elastico = GaiaElastico()
             self.opensearch_cluster_alias = 'opensearch_worker_fast' #  get dynamically
             self.opensearch_index_alias = ''
             self.opensearch_index_name = ''

    def _configure_augmenters(self, augmenters: List):
        for augmenter in augmenters:
            dd = {}
            cls = augmenter()
            cls.worker_id = self.worker_id
            dd['cls'] = cls

            if self.set_opensearch:
                elastico = GaiaElastico()
                elastico.connect(alias=self.opensearch_cluster_alias)
            self.augmenters_config[cls.formatted_name] = dd
            print(f'augmenter props set for: { cls.formatted_name }')

    def opensearch_incremental_writer(self, raw_record=None):
        """!Not currently used."""

        N = 10000  #  array length for insertion

        #  prep record
        rec = {'_source': raw_record}
        rec['_type'] = '_doc'
        rec['_index'] = self.opensearch_idx
        rec['_id'] = raw_record['goid']

        if self.opensearch_records and len(self.opensearch_records):
             self.opensearch_records.append(rec)
        else:
             self.opensearch_records = [rec]

        if len(self.opensearch_records) >= N:
            #  insert
            self.elastico.insert_bulk(self.opensearch_records, multi=True)
            #  empty queue
            self.opensearch_records = []

    def process_batch(self, batch=None):
        parse_uuid_to_goid = False

        #  input data
        data_dict = {}
        counted = 0

        for row in batch:

            #  Legacy: only if source is CrunchBase
            if parse_uuid_to_goid:
                cbuuid = row['uuid']
                goid = build_goid(src='cbuuid', oid_type='org', srckey=cbuuid, dominant=False)
            else:
                goid = row['GOID']

            #  this data dict is passed to all augmenters
            data_dict[goid] = {
                'concat_description': concat_description(row),
                'homepage_url': row.get('homepage_url'),
                'source': row
            }

            counted += 1

        print('counted: ', counted)
        print('data_dict len: ', len(data_dict.keys()))
        #  dict keyed by goid
        output_dict = {}

        #  iterate augmenters
        for key, props in self.augmenters_config.items():
            augmenter = props['cls']
            print(f'////////// augment batch. { augmenter.formatted_name} ')
            #  augment and cache records

            try:
                augmented_dict = self.augment_and_cache_records(augmenter, data_dict)
            except Exception as e:
                print('++++++++++++++++++++ AUGMENT AND CACHE ERROR', e)
                import traceback
                tb = traceback.format_exc()
                print('traceback', tb)
                import pdb; pdb.set_trace()

            # TODO: uneeded?
            #for goid, value in augmented_dict.items():
            #    try:
            #         output_dict[goid][augmenter.formatted_name] = value
            #    except KeyError as e:
            #         output_dict[goid] = {augmenter.formatted_name: value}

        #  insert into opensearch [only to be used if opensearch is to be used as a cache]
        #if self.set_opensearch and self.elastico:
        #    self.opensearch_insert(cache_dict)

        #return output_dict

    @staticmethod
    def augment_and_cache_records(augmenter=None, data_dict=None):
        augmented_dict = augmenter.compute_with_cache(data_dict, refresh_cache=REFRESH_AUGMENTER_CACHE)

        #  sanity check
        if len(augmented_dict.keys()) != len(data_dict.keys()):
            print('Omni Record Augmenter Error: augmented length mismatch.....')
            #import pdb; pdb.set_trace()

        return augmented_dict

    def post_build(self):
        for k, v in self.augmenters_config.items():
            augmenter_cls = v['cls']
            augmenter_cls.close()

            #  kv_to_pq
            src_path = augmenter_cls.cache_path
            pq_path = os.path.join(gfs_folder_local(tree='gfs_omnibuilder_pipeline'), 'llm_output', augmenter_cls.formatted_name)
            if not os.path.exists(pq_path):
                print('making path... ', pq_path)
                os.mkdir(pq_path)
            #print('*** WARNING: PQ disbabled! ***')
            kv2pq(src_path=src_path, dest_path=pq_path)
            print(f'\n////////// Augmenter Cache Path: {augmenter_cls.cache_path}\n')

    def opensearch_insert(self, augmented_dict=None):
        #  list of all records
        #  with opensearch metadata
        augmented_records = [
            {
                '_type': '_doc',
                '_index': self.opensearch_index_name,
                '_id': goid,
                '_source': {'goid': goid, **dd}
            }
            for goid, dd in augmented_dict.items()
        ]
        #if self.set_opensearch:
        #    opensearch_incremental_writer(self, raw_record=augmented_rec)

        #  send whole batch to opensearch
        self.elastico.insert_bulks(augmented_records)

        return augmented_records







@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-g', '--group', type=str, default=None)
@click.option('-s', '--src_path', type=str, default=None)
@click.option('-o', '--offset', type=int, default=0)
@click.option('-m', '--multiprocess', is_flag=True, default=False)
@click.option('-i', '--increment_cache', is_flag=True, default=False)
def run(group=None, batch_amt=None, hard_limit=None, full_update=False, src_path=None, offset=None, multiprocess=False, increment_cache=False):
    print(f'local kwargs: {locals()}')

    if src_path and src_path.split('.')[1] not in ('pq', 'parquet'):
        raise NotImplementedError('only parquet sources supported!')

    if src_path is None:
        raise ValueError('src_path required!')


    MULTIPROCESS = multiprocess
    start_row = offset

    #  create generator from source (batches of batch_amt)
    if start_row:
        print('////////// slice parquet gen')
        src_gen = parquet_generator_slice(
            file_path=src_path,
            batch_size=batch_amt,
            start_row=start_row
        )
    else:
        print('////////// vanilla parquet gen')
        src_gen = parquet_generator(file_path=src_path, batch_size=batch_amt)

    augmenters = get_augmenters_by_group(group)

    if len(augmenters) < 1:
        raise ValueError('no augmenters registered!')

    if MULTIPROCESS:
        # --- instantiate queue, workers ---
        queue, workers = launch_omni_augmenter_workers(augmenters)
    else:
        # --- instantiate omni augmenter ---
        omni_augmenter = OmniRecordAugmenter(augmenters=augmenters)

    count = 0
    if start_row:
        print(f'''
                ======================
                START ROW: {start_row}
                ======================
                '''
              )
        count += start_row
    print(f'START: count: {count}, hard_limit: {hard_limit}')

    #  iterate source
    batch_count = 0
    for batch in batch_generator(src=src_gen, batch_size=batch_amt):
        batch_count += 1
        # print('augment and cache batch...')
        if MULTIPROCESS:
            queue.put(batch)
        else:
            omni_augmenter.process_batch(batch)

        #  increment count
        count += len(batch)
        print(
            f'//////////////////////////////////////////////////////////////////////////////////////////////////// count: {count}')

        #  *** TERMINATE ***: end of source data
        if len(batch) < batch_amt:
            print('no more records.')
            break

        #  *** TERMINATE ***: hard_limit reached
        if hard_limit and count >= hard_limit:
            print(f'hard limit reached. count: {count}, hard_limit: {hard_limit}')
            break

    #  post build steps
    if MULTIPROCESS:
        [queue.put('DIE') for p in workers]
        [p.join() for p in workers]
        #  we need an instance of OmniRecordAugmenter for post build steps
        omni_augmenter = OmniRecordAugmenter(augmenters=augmenters)

    #  cleanup
    omni_augmenter.post_build()
    print('done!')


if __name__ == '__main__':
    run()
