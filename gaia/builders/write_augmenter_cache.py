#  usage:  python -m gaia.builders.write_augmenter cache -b 250 -g agbase

#  adjacent importing
import os
import sys
import click
import sqlite3
import shutil
import time
import json
#  add modules to python path for adjacent importing (python 2.7 way)
parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)

#  es connect
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico

from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_fs.paths import gfs_folder_local

#  local
from gaia.builders import augmenters
from gaia.builders.builders import AgsearchBuilder
from gaia.builders.decorators import timethis


#  cache destination
#  all caches writtern to /build initially
#  moved to /datasets on success
CACHE_PATH = gfs_folder_local(tree='gfs_datasets')
BUILD_CACHE_PATH = os.path.join(CACHE_PATH, 'build')

WRITE_COUNT = 0


#SRC = 'opensearch'
SRC = 'kv'

#  source (OpenSearch)
if SRC == 'opensearch':
    SRC_ALIAS = 'opensearch_worker_fast'
    SRC_IDX = 'idx_cb10_fresh1'
    elastico = GaiaElastico()
    elastico.connect(alias=SRC_ALIAS)
    #  get live index - most recent
    LATEST_IDX = list(elastico.es.indices.get('agsearch_cb'))[0]
    print(f'LATEST_IDX: {LATEST_IDX}')


#  augmenetrs cache for groups
spiders = (
    #augmenters.Agspider,
    #augmenters.Babyspider, # don't need to write - hasn't been updated
    #augmenters.BabyspiderPlaintext,
    augmenters.LangDetectWeb,
    augmenters.LangDetectSummary,
    #augmenters.Baby2Plaintext,
)

agbase = (
    augmenters.Dealer,
    augmenters.Investor,
    augmenters.People,
    augmenters.Rounds,
    augmenters.Agbase,
)

investors = (
    augmenters.Investor,
)

ml = (
    augmenters.Xform,
    augmenters.Knn,
    augmenters.Mlorg
)

mlorg = (
    augmenters.Mlorg,
)

subs = (
    augmenters.SubscribersByDomain,
)

babyspider = (
    augmenters.Babyspider,
)


sbert = (
    augmenters.SBert_EMB02xlm,
)

#openai = (
#    augmenters.GaiaDescribe,
#)


def get_batch(builder=None, cb_res=None, batch_amt=None):
    batch = builder.get_batched(generator=cb_res, batch_amt=batch_amt)
    # cached, uuids_homepage_dict, uuids_doc_dict
    data_dict = builder.es_unpack(
        batch=batch,
        dest_idx=builder.dest_idx
    )
    #  {'cached', Dict, 'uuids_doc_dict': Dict, 'uuids_homepage_dict': Dict}
    return data_dict


def process_batch(augmenter=None, kv=None, data_dict=None):
    # TODO: envelopize data
    global WRITE_COUNT
    computed_dict = augmenter.compute(data_dict=data_dict)
    #  key/ value store write cache
    print('writing {} cache entries for {}'.format(len(computed_dict.keys()), augmenter.formatted_name))
    if 0:
        print(f'skipping builder cache for {augmenter.formatted_name}')
    else:
        for key, value in computed_dict.items():
            #print(f'DEBug!!!!set cache key: {key}')
            kv.set(key, value)
    WRITE_COUNT += len(computed_dict.keys())
    print(f'~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ WRITE_COUNT: {WRITE_COUNT}')



def augmenters_by_group(group=None, globals=globals()):
    #  augmenter can be group or member
    if '.' in group:
        subgroup = group.split('.')[1]
        augmenters = globals[group.split('.')[0]]
        augmenters = [a for a in augmenters if a().formatted_name == subgroup]
    else:
        #  convert str arg to variable
        augmenters = globals[group]
    return augmenters


def update_augmenter_path(augmenter):
    src_path = os.path.join(BUILD_CACHE_PATH, f'{augmenter.formatted_name}.db')
    dest_path = os.path.join(CACHE_PATH, f'{augmenter.formatted_name}.db')

    src_kv = KeyValueStore_sqlite3(file_path=src_path)
    dest_kv = KeyValueStore_sqlite3(file_path=dest_path)

    #  count rows (fast method)
    print(f'kv counts: src: {src_kv.count_fastupperlim()}, dest: {dest_kv.count_fastupperlim()}')
    if src_kv.count_fastupperlim() >= dest_kv.count_fastupperlim():
        print('/////////////////////////////////////////////// moving to dest path...', dest_path)
        shutil.move(src_path, dest_path)
    else:
        print('/////////////////////////////////////////////// src_path is smaller than dest_path so will not move!', [src_path, dest_path])


#@click.command()
#@click.option('-b', '--batch_amt', type=int, default=250)
#@click.option('-l', '--hard_limit', type=int, default=None)
#@click.option('-g', '--group', type=str, default=None)
#@click.option('-f', '--full_update', is_flag=True, default=False)
#def run(group=None, batch_amt=None, hard_limit=None, full_update=False):

#    #  which augmenters to use - can be group or singular (e.g. agbase | agbase.dealer)
#    augmenters = augmenters_by_group(group)
#    build_cache(augmenters, batch_amt, hard_limit, full_update)


def build_cache(augmenters=None, batch_amt=None, hard_limit=None, full_update=False, src_kv_path=None):
    """Build Cache for given Augmenters."""

    #  used for get batch
    builder = AgsearchBuilder()

    #if full_update and SRC == 'opensearch':
    #    print('scan opensearch, full update...')
    #    #  full scan of OpenSearch (memory intensive)
    #    cb_res, _ = elastico.docs_scan(
    #        index=SRC_IDX,
    #        pp_count=batch_amt
    #    )

    if full_update and src_kv_path:
        #  src is KV iterator
        src_kv = KeyValueStore_sqlite3(file_path=src_kv_path)
        src_kv.cursor.execute(f"SELECT * FROM main_table")


    #  initialize count
    HARD_COUNT = 0
    CONTINUE_FROM = None

    #  holds keyvalue instances
    kv_dict = {}
    augmenter_instances = []

    for augmenter_cls in augmenters:
        #  create augmenter instances
        augmenter = augmenter_cls()
        augmenter_instances.append(augmenter)

        #  dict of KV instances specific for augmenters (filepath)
        file_path = os.path.join(BUILD_CACHE_PATH, f'{augmenter.formatted_name}.db')
        kv = KeyValueStore_sqlite3(file_path=file_path)
        kv_dict[augmenter.formatted_name] = kv

        #  reset kv if full_update
        if full_update and CONTINUE_FROM is None:
            print('reset kv... ', kv.file_path)
            kv.reset()

    #  es offset
    pp_from = 0

    #  loop through generator results
    while True:
        #  FULL UPDATE
        if src_kv:
            print(f'get batch from {src_kv.file_path}...')
            batch = next(
                src_kv.get_batch_generator(batch_size=batch_amt), None)
            data_dict = builder.es_unpack(
                batch=batch,
                dest_idx=builder.dest_idx
            )

        elif SRC == 'opensearch':
            print(f'get batch from idx...{SRC_IDX}')
            data_dict = get_batch(
                builder=builder,
                cb_res=cb_res,
                batch_amt=batch_amt
            )

        print(f'data_dict size: {len(data_dict.keys())}')

        #  skip n rows CONTINUE_FROM not None
        if CONTINUE_FROM and HARD_COUNT < CONTINUE_FROM:
            HARD_COUNT += batch_amt
            print('skipping... HARD COUNT: {}'.format(HARD_COUNT))
            continue

        #  process augmenters
        for augmenter in augmenter_instances:
            try:
                print('augmenting batch...')
                process_batch(
                    augmenter=augmenter,
                    kv=kv_dict[augmenter.formatted_name],
                    data_dict=data_dict,
                )
            except Exception as e:
                print('process augmenters error', e)
                import traceback
                print(traceback.format_exc())
                raise

        #  break if all records done
        if len(data_dict['cached']) != batch_amt:
            print('cb_cache out. No more records!')
            if src_kv:
                src_kv.close()

            for augmenter in augmenter_instances:
                kv = kv_dict[augmenter.formatted_name]
                print(f'closing kv... {kv.file_path}')
                kv.close()
                print(f'update path for {augmenter.formatted_name}')
                update_augmenter_path(augmenter)
            break

        #  break if HARD_LIMIT reached
        HARD_COUNT += batch_amt
        print('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ HARD COUNT: {}'.format(HARD_COUNT))
        if hard_limit and HARD_COUNT >= hard_limit:
            print('HARD LIMIT REACHED')
            if src_kv:
                src_kv.close()
            for augmenter in augmenter_instances:
                kv_dict[augmenter.formatted_name].close()
                #update_augmenter_path(augmenter)
            break

        #  increment ES offset
        pp_from += batch_amt



@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-g', '--group', type=str, default=None)
@click.option('-f', '--full_update', is_flag=True, default=False)
def run(group=None, batch_amt=None, hard_limit=None, full_update=False):

    #  which augmenters to use - can be group or singular (e.g. agbase | agbase.dealer)
    augmenters = augmenters_by_group(group)

    #  source is kv store
    src_kv_path = os.path.join(CACHE_PATH, 'cb10.db')
    print(f'src_kv_path: {src_kv_path}, full update...')

    build_cache(augmenters, batch_amt, hard_limit, full_update, src_kv_path)

if __name__ == '__main__':
    run()

