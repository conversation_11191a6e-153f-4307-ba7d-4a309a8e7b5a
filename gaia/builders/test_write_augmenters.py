import os
import sys

parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)

#  es connect
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico

#  key value store
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3


#  local
from gaia.builders import augmenters

def run():
    kv = KeyValueStore_sqlite3(file_path='./{}.db'.format('__test_dealer'))
    aug = augmenters.Dealer()
    data_dict = {'uuids_doc_dict': {'12082774-e32f-43f4-9a9f-1a2cb14569bc': ''}}
    computed_dict = aug.compute(data_dict=data_dict)
    for key, value in computed_dict.items():
        kv.set(key, value)


if __name__ == '__main__':
    run()
