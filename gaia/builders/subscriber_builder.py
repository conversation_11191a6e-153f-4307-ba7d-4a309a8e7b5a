import csv
import ast
import os
import requests
import json
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_fs.paths import gfs_folder_local


CONVERTKIT_SECRET_API_KEY = os.environ['CONVERTKIT_SECRET_API_KEY']
CONVERTKIT_CSV_PATH = './DATA/**********************.csv'
MAILCHIMP_CSV_PATH = 'subscribed_members_export_561283a165.csv' #  static: MC lists are no longer updated


def csv_generator(file_path, delimiter=','):
    with open(file_path, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile, delimiter=delimiter)
        for row in reader:
            yield row


def parquet_generator(file_path, batch_size=1000):
    parquet_file = pq.ParquetFile(file_path)

    for batch in parquet_file.iter_batches(batch_size=batch_size):
        batch_df = batch.to_pandas().to_dict(orient='records')
        yield batch_df


def batch_generator(src, batch_size=100):
    batch = []
    for item in src:

        if isinstance(item, list):
            for row in item:
                batch.append(row)
                if len(batch) == batch_size:
                    yield batch
                    batch = []

        elif isinstance(item, dict):
            batch.append(item)
            if len(batch) == batch_size:
                yield batch
                batch = []
    if batch:
        yield batch



def get_ck_subs():
    print('getting ck subs...')
    base_url = 'https://api.convertkit.com/v3/subscribers/?api_secret={}'.format(CONVERTKIT_SECRET_API_KEY)
    csv_file = CONVERTKIT_CSV_PATH

    with open(csv_file, 'w') as csvfile:
        fieldnames = ['id', 'first_name', 'last_name', 'email_address', 'state', 'created_at', 'fields']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        #writer.writerow(fieldnames)

    page = 1
    count = 0
    while True:
        print(f'getting ck page {page}...')
        url = base_url + '&page={}'.format(page)
        try:
            res = requests.get(url)
        except Exception as e:
            print(e)
            import pdb; pdb.set_trace()

        try:
            subscribers = res.json()['subscribers']
            count += len(subscribers)
            print('count: ', count)
        except Exception as e:
            print('error', e)

        if len(subscribers) < 1:
            break

        #print('first row', subscribers[0])

        with open(csv_file, 'a') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            for user in subscribers:
                writer.writerow(user)

        page += 1
    return csv_file


def prepare_batch(idx=None, batch=None, list=None):
    print('prepare batch...')
    output_batch = []
    retain_keys = ['First Name', 'Last Name', 'Email Address', 'created_at', 'CONFIRM_TIME']
    for row in batch:
       #print('row to filter', row)
       row['Email Address'] = row['email_address']

       if list == 'convertkit':
           fields = ast.literal_eval(row['fields'])
           row['First Name'] = row['first_name']
           row['Last Name'] = row['last_name']
           if row['Last Name'] in [None, ''] and fields.get('last_name'):
               row['Last Name'] = fields['last_name']

       #  filter and normalize key names
       filtered_row = {k.lower().replace(' ', '_'): v for k, v in row.items() if k in retain_keys}
       filtered_row['list_source'] = list

       if idx:
           filtered_row['_type'] = '_doc'
           filtered_row['_index'] = idx
       #filtered_row['_id'] = ''
       #print('filtered_row', filtered_row)
       output_batch.append(filtered_row)
    return output_batch


#@click.command()
#@click.option('-c', '--cluster', type=str, default='agsearch_worker_fast')
#@click.option('-idx', '--index', type=str, default=None)
#@click.option('-b', '--batch_size', type=int, default=1000)
#@click.option('-src', '--source', type=str, default=None)
def build(cluster=None, index=None, batch_size=1000, src=None, recreate_index=True, list=None):
    elastico = GaiaElastico()
    elastico.connect(alias=cluster)
    #  mapping
    body = {
        "settings": {
            "index.mapping.total_fields.limit": 100,
            "index.refresh_interval": -1,
        },
        "mappings": {
            "properties": {
                "email": {
                    "type": "keyword",
                    "fields": {
                        "analyzed": {
                            "type": "text"
                        }
                    }
                 }
             }
        }
    }
    if recreate_index:
        try:
            elastico.es.indices.create(index=index, body=body)

        except Exception as e:
            #  idx exists
            print('error: ', e)
            print('deleting index and recreating...')
            elastico.index_delete(index=index)
            elastico.es.indices.create(index=index, body=body)

    count = 0
    for batch in batch_generator(src=src, batch_size=batch_size):
        filtered_batch = prepare_batch(idx=index, batch=batch, list=list)
        elastico.insert_bulk(bulks=filtered_batch, multi=True)
        count += len(filtered_batch)
        print(f'count: {count}')

    elastico.es.indices.put_settings(
        index=index,
        body={"refresh_interval": "30s", },
        ignore_unavailable=True
    )



def cache_by_domain(src=None, kv=None, batch_size=1000, list=None):
    print('cache by domain. loading EMAIL_EXCLUDES...')
    file_path = '/usr/local/agfunder/agbase_admin_min37_dj200/gaia/builders/email_vendor_exclude_list.txt'
    EMAIL_EXCLUDES = []
    with open(file_path, 'r') as file:
        for domain in file:
            domain = domain.strip()
            EMAIL_EXCLUDES.append(domain)

    count = 0
    for batch in batch_generator(src=src, batch_size=batch_size):
        filtered_batch = prepare_batch(idx=None, batch=batch, list=list)
        for row in filtered_batch:
            new_row = {}
            for key in row.keys():
                new_row[key.replace(' ', '_').lower()] = row[key]

            #  get domain from email
            try:
                domain = new_row['email_address'].split('@')[1]
            except Exception as e:
                import pdb; pdb.set_trace()

            if domain in EMAIL_EXCLUDES:
                continue

            #  if domain exists, append users
            if kv.get(domain):
                user_list = kv.get(domain)
                user_list.append(new_row)
            #  else add new uer list
            else:
                user_list = [new_row]

            kv.set(domain, user_list)
        count += len(filtered_batch)
        print(f'count: {count}')



if __name__ == '__main__':

    ES_CLUSTER = 'opensearch_worker_fast'
    ES_INDEX = 'idx_agfunder_subscribers1'
    BATCH_SIZE = 1000

    #  CONVERTKIT  ---
    if 0:
        get_ck_subs()

    if 0:
        src_csv_path = CONVERTKIT_CSV_PATH
        src_gen = csv_generator(file_path=src_csv_path)
        build(
            cluster=ES_CLUSTER,
            index=ES_INDEX,
            batch_size=BATCH_SIZE,
            src=src_gen,
            recreate_index=True,
            list='convertkit'
        )

    #  MAILCHIMP  ---
    if 0:
        src_csv_path = MAILCHIMP_CSV_PATH
        src_gen = csv_generator(file_path=src_csv_path)
        build(
            cluster=ES_CLUSTER,
            index=ES_INDEX,
            batch_size=BATCH_SIZE,
            src=src_gen,
            recreate_index=False,
            list='mailchimp'
        )

    #  For subscribersbydomain augmenter (AgSearch)  ---
    if 1:
       path = CONVERTKIT_CSV_PATH
       src_gen = csv_generator(file_path=path)
       CACHE_PATH = gfs_folder_local(tree='gfs_datasets')
       kv_path = os.path.join(CACHE_PATH, 'subscribers.db')
       kv = KeyValueStore_sqlite3(file_path=kv_path)

       print('resetting cache...', kv.file_path)
       kv.reset()

       print('cache by domain...')
       cache_by_domain(src=src_gen, kv=kv, list='convertkit')
