#  usage:  python -m gaia.builders.write_augmenter cache -b 250 -g agbase

#  adjacent importing
import os
import sys
import click
import csv
import shutil
import pyarrow.parquet as pq
from datetime import datetime
import tarfile
#  add modules to python path for adjacent importing (python 2.7 way)
parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)

#  es connect
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico

from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_fs.paths import gfs_folder_local

#  local
from gaia.builders import augmenters
from gaia.builders.builders import AgsearchBuilder
from gaia.builders.omni_builder import OmniMultiProcessWorker
from gaia.builders.decorators import timethis

from gaia.core.gaia_goid import build_goid
from gaia.util.formats.frame_incremental import PolarsParquetWriter, PandasCSVWriter

from multiprocessing import Pool

#  gaiafs root
#  TODO: tree, subfolder
CACHE_PATH = gfs_folder_local(tree='gfs_datasets')
CACHE_PATH += '/from_frame'
BUILD_CACHE_PATH = os.path.join(CACHE_PATH, 'build')
print(f'BUILD CACHE PATH: { BUILD_CACHE_PATH }')
#  get live index - most recent
#elastico = GaiaElastico()
#elastico.connect(alias='opensearch_worker')


#  augmenetrs cache for groups
spiders = (
    #augmenters.Agspider,
    #augmenters.Babyspider, # don't need to write - hasn't been updated
    #augmenters.BabyspiderPlaintext,
    augmenters.LangDetectWeb,
    augmenters.LangDetectSummary,
)

agbase = (
    augmenters.Dealer,
    augmenters.Investor,
    augmenters.People,
    augmenters.Rounds,
    augmenters.Agbase,
)

ml = (
    augmenters.Xform,
    ###augmenters.Knn,
    augmenters.SBert_EMB02xlm,
)

mlorg = (
    augmenters.Mlorg,
)


def flatten_dict(dd, separator ='__', prefix = ''):
    return { prefix + separator + k if prefix else k : v
             for kk, vv in dd.items()
             for k, v in flatten_dict(vv, separator, kk).items()
             } if isinstance(dd, dict) else { prefix : dd }


def csv_generator(file_path, delimiter=','):
    with open(file_path, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile, delimiter=delimiter)
        for row in reader:
            yield row


def parquet_generator(file_path, batch_size=1000):
    parquet_file = pq.ParquetFile(file_path)
    for batch in parquet_file.iter_batches(batch_size=batch_size):
        batch_df = batch.to_pandas().to_dict(orient='records')
        yield batch_df


def batch_generator(src, batch_size=100):
    """
    Generate Batch from any iterable src.
    """
    batch = []
    for item in src:

        if isinstance(item, list):
            for row in item:
                batch.append(row)
                if len(batch) == batch_size:
                    yield batch
                    batch = []

        elif instance(item, dict):
            batch.append(item)
            if len(batch) == batch_size:
                yield batch
                batch = []
    if batch:
        yield batch

def concat_description(row):
    try:
        doc = str(row.get("name") or '') + " " + \
              str(row.get("short_description") or '') + " " + \
              str(row.get("description") or '')
    except Exception as e:
        import pdb; pdb.set_trace()
    return doc


def get_pq_writer(augmenter_name=None):
    dir_path = os.path.join(BUILD_CACHE_PATH, 'parquet', augmenter_name)
    print(f'parquet file path: {dir_path}')
    return PolarsParquetWriter(folder=dir_path, N=20000)


def get_csv_writer(augmenter_name=None):
    dir_path = os.path.join(BUILD_CACHE_PATH, 'csv', augmenter_name)
    return PandasCSVWriter(folder=dir_path, N=20000)


def get_kv_writer(augmenter_name=None, reset=False):
    file_path = os.path.join(BUILD_CACHE_PATH, 'kv', f'{augmenter_name}.db')
    print(f'kv file path: {file_path}')
    kv = KeyValueStore_sqlite3(file_path=file_path)
    kv.set_mode(mode="unsafe_cheetah", cursor=None)
    if reset:
        print('resetting kv cache...')
        kv.reset(delete=True)
    return kv


'''
#  not currently implemented
def worker_augment_and_cache(augmenter, data_dict):
    kv = get_kv_writer(augmenter.formatted_name)
    #pq = get_pq_writer(augmenter.formatted_name)
    pq = None
    augmented_dict = augmenter.run(data_dict)
    #  iterate augmented batch
    for goid, value in augmented_dict.items():
        if kv:
            kv.set(goid, value)
        if pq:
            #  format is goid
            row = {'goid': goid}
            preds = flatten_dict(value)
            row.update(preds)
            pq.accept(row)
'''

def update_augmenter_path(augmenter_formatted_name: str) -> str:
    """Move cache from build location to cache location."""

    for cache in ['pq', 'kv']:
        if cache == 'pq':

            #  PQ
            src_path = os.path.join(BUILD_CACHE_PATH, 'pq', f'{augmenter_formatted_name}.pq')
            dest_path = os.path.join(CACHE_PATH, 'pq_new', f'{augmenter_formatted_name}.pq')

        elif cache == 'kv':

            #  KV
            src_path = os.path.join(BUILD_CACHE_PATH, 'kv', f'{augmenter_formatted_name}.db')
            dest_path = os.path.join(CACHE_PATH, 'kv_new', f'{augmenter_formatted_name}.db')

            src_kv = KeyValueStore_sqlite3(file_path=src_path)
            dest_kv = KeyValueStore_sqlite3(file_path=dest_path)

            #  count rows (fast method)
            print(f'kv counts: src: {src_kv.count_fastupperlim()}, dest: {dest_kv.count_fastupperlim()}')

        if os.path.getsize(src_path) >= os.path.getsize(dest_path):
            print('/////////////////////////////////////////////// moving to dest path...', dest_path)
            shutil.move(src_path, dest_path)
        else:
            print('/////////////////////////////////////////////// src_path is smaller than dest_path so will not move!', [src_path, dest_path])



class OmniRecordAugmenter():

    def __init__(self, augmenters=None, reset_cache=False):
         self.augmenters = augmenters
         self.set_kv = True
         self.set_pq = True
         #  we can send output to OpneSearch too
         self.set_opensearch = False
         self.elastico = GaiaElastico()
         self.opensearch_cluster_alias = 'opensearch_worker_fast'
         self.opensearch_index_alias = ''
         self.opensearch_index_name = ''
         self.augmenters_config = {}
         self._configure_augmenters(reset_cache)

    def _configure_augmenters(self, reset_cache=False):
        for aug in self.augmenters:
            dd = {}
            cls = aug()
            dd['cls'] = cls
            if self.set_kv:
                dd['kv'] = get_kv_writer(cls.formatted_name, reset_cache)
            if self.set_pq:
                dd['pq'] = get_pq_writer(cls.formatted_name)
            if self.set_opensearch:
                elastico.connect(alias=self.opensearch_cluster_alias)
            self.augmenters_config[cls.formatted_name] = dd
            print(f'augmenter props set for { cls.formatted_name }')


    def opensearch_incremental_writer(self, raw_record=None):
        N = 10000  #  array length for insertion

        #  prep record
        rec = {'_source': raw_record}
        rec['_type'] = '_doc'
        rec['_index'] = self.opensearch_idx
        rec['_id'] = raw_record['goid']

        if self.opensearch_records and len(self.opensearch_records):
             self.opensearch_records.append(rec)
        else:
             self.opensearch_records = [rec]
        if len(self.opensearch_records) >= N:
            #  insert
            self.elastico.insert_bulk(self.opensearch_records, multi=True)
            #  empty queue
            self.opensearch_records = []


    def process_batch(self, batch=None):

        data_dict = {}

        parse_uuid = False

        for row in batch:
            #  only if source is CrunchBase
            if parse_uuid:
                cbuuid = row['uuid']
                goid = build_goid(src='cbuuid', oid_type='org', srckey=cbuuid, dominant=False)

            else:
                goid = row['GOID']

            data_dict[goid] = {
                'concat_description': concat_description(row),
                'homepage_url': row.get('homepage_url')
            }

        #  dict keyed by goid
        augmented_dict = {}

        #  iterate augmenters
        for key, props in self.augmenters_config.items():
            augmenter = props['cls']

            print(f'.............................................augment batch. { augmenter.formatted_name} ')

            #  --- augment and cache records ---
            augmented_dict = self.augment_and_cache_records(augmenter, data_dict)

            #  --- prep batch for opensearch ---
            for goid, value in augmented_dict.items():
                try:
                     augmented_dict[goid][augmenter.formatted_name] = value
                except KeyError as e:
                     augmented_dict[goid] = {augmenter.formatted_name: value}

        #  insert into opensearch
        if self.set_opensearch and self.elastico:
            self.opensearch_insert(augmented_dict)

        return augmented_dict


    def opensearch_insert(self, augmented_dict=None):
        #  list of all records
        #  with opensearch metadata
        augmented_records = [
            {
                '_type': '_doc',
                '_index': self.opensearch_index_name,
                '_id': goid,
                '_source': {'goid': goid, **dd}
            }
            for goid, dd in augmented_dict.items()
        ]
        #if self.set_opensearch:
        #    opensearch_incremental_writer(self, raw_record=augmented_rec)

        #  send whole batch to opensearch
        self.elastico.insert_bulks(augmented_records)

        return augmented_records



    def augment_and_cache_records(self, augmenter=None, data_dict=None):
        cfg = self.augmenters_config[augmenter.formatted_name]
        kv = cfg.get('kv')
        pq = cfg.get('pq')

        #  --- augment ---
        augmented_dict = augmenter.run(data_dict)

        #  --- cache ---
        #  iterate augmented batch
        for goid, value in augmented_dict.items():
            if self.set_kv and kv:
                kv.set(goid, value)
            if self.set_pq and pq:
                #  format is goid
                row = {'goid': goid}
                #  here we are flattening dict
                #  so that each key column is a key
                preds = flatten_dict(value)
                row.update(preds)
                pq.accept(row)

        return augmented_dict


    def _close_kvs(self):
        print('closing kvs....')
        for dd in self.augmenters_config.values():
            print("closing kv .... {dd['kv'].file_path}")
            dd['kv'].close()


    def post_build(self):
        for key, dd in self.augmenters_config.items():
            augmenter = dd['cls']

            #  mv cache
            update_augmenter_path(augmenter.formatted_name)

            """
            if self.set_pq:
                #  get augmenter pq instance
                pq = dd['pq']
                pq_dir = os.path.dirname(pq._get_path())
                dest_dir = pq_dir.replace(augmenter.formatted_name, 'compressed')
                print('pq_dir, dest_dir', pq_dir, dest_dir)
                current_time = datetime.now()
                timestamp_str = current_time.strftime("%Y%m%d%H%M%S")
                #  compress pq_dir
                output_file = os.path.join(dest_dir, f'{augmenter.formatted_name}_{timestamp_str}.tar.gz')
                print('compressing to...', output_file)
                with tarfile.open(output_file, "w:gz") as tar:
                    tar.add(pq_dir, arcname="")
            """





@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-g', '--group', type=str, default=None)
@click.option('-f', '--full_update', is_flag=True, default=False)
#@timethis
def run(group=None, batch_amt=None, hard_limit=None, full_update=False):

    multiprocess = False
    reset_cache = True

    print(f'kwargs: {locals()}')

    #  generator from csv file
    #src_csv_path = '/home/<USER>/agbase_admin_min37_dj200/DATA/cb_dnld_20230721/organizations.csv'
    #src_gen = csv_generator(file_path=src_csv_path)

    src_pq_path = '/var/lib/gaia/GAIA_FS/frames/omni/org_merge/frame.parquet'
    src_gen = parquet_generator(file_path=src_pq_path) #  batches of 1000 rows

    if '.' in group:
        subgroup = group.split('.')[1]
        augmenters = globals()[group.split('.')[0]]
        augmenters = [a for a in augmenters if a().formatted_name == subgroup]
    else:
        #  convert str arg to variable
        augmenters = globals()[group]

    if len(augmenters) < 1:
        raise ValueError('no augmenters registered!')


    if not multiprocess:
        # --- call omni augmenter ---
        omni_augmenter = OmniRecordAugmenter(augmenters, reset_cache)

    count = 0
    continue_from = None

    for batch in batch_generator(src=src_gen, batch_size=batch_amt):
        if continue_from and count < continue_from:
            print('skipping...')
        else:
            print('augment and cache batch...')
            if not multiprocess:
                omni_augmenter.process_batch(batch)

            else:
                raise NotImplementedError('multiprocessing is disabled!')
                """
                N = multiprocessing.cpu_count() - 1 or 1
                queue = multiprocessing.Queue()
                workers = []
                callback = augmenter_cache.augment_and_cache
                for i in range(N):
                    augmenter_cache = OmniAugmenterCache()
                    omni_worker = OmniMultiPrcoessWorker()
                    insert_worker = multiprocessing.Process(
                        target=omni_worker,
                        args=(i+1, queue, callback))
                    #insert_worker.daemon = True

                    #  start all workers
                    insert_worker.start()

                    #  keep reference to all workers
                    workers.append(insert_worker)
                """

        #  *** TERMINATE ***: end of source data
        if len(batch) < batch_amt:
            print('no more records.')
            omni_augmenter._close_kvs()
            break

        #  increment count
        count += len(batch)
        print(f'.............................................count: { count }')

        #  *** TERMINATE ***: hard_limit reached
        if hard_limit and count >= hard_limit:
            print('hard limit reached.')
            omni_augmenter._close_kvs()
            break

    #  post build steps
    omni_augmenter.post_build()
    print('done!')

if __name__ == '__main__':
    run()
