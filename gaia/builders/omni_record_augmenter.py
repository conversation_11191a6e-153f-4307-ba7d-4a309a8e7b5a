#  usage:  python -m gaia.builders.omni_record_augmenter -b 1 -g ml.xform -l 1
import os
import sys
import click
import csv
import shutil
import tarfile
import pandas as pd
import pyarrow.parquet as pq
import datetime
import json
import time
import multiprocessing
import numpy as np
from decimal import Decimal
from datetime import datetime
from typing import List
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.gaia_fs.paths import gfs_folder_local
from gaia.builders import augmenters_omni as augmenters
from gaia.builders import augmenters_vendors
from gaia.builders import augmenters_web
from gaia.builders.batch_generators import csv_generator, parquet_generator, batch_generator, parquet_generator_slice, parquet_generator_sample
from gaia.core.gaia_goid import build_goid
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.util.formats.frame_incremental import PolarsParquetWriter, PandasParquetWriter, PandasCSVWriter
from gaia.builders.kv_to_pq import kv2pq

from gaia.util.formats.frame_incremental import NumpyArrayWriter
from gaia.util.keyvector import keyvector

#  *** CACHE PATHS ***
CACHE_PATH = gfs_folder_local(tree='gfs_datasets')
CACHE_PATH += '/from_frame'
#BUILD_CACHE_PATH = os.path.join(CACHE_PATH, 'build')
#print(f'BUILD CACHE PATH: { BUILD_CACHE_PATH }')
CACHE_PREFIX = ''


def curried_constructor(cls, **kwargs):
    def constructor():
        return cls(**kwargs)
    return constructor


#  augmenters as groups
spiders = (
    #augmenters.Agspider,
    #augmenters.Babyspider, # don't need to write - hasn't been updated
    #augmenters.BabyspiderPlaintext,
    augmenters.LangDetectWeb,
    augmenters.LangDetectSummary,
)
agbase = (
    augmenters.Dealer,
    augmenters.Investor,
    augmenters.People,
    augmenters.Rounds,
    augmenters.Agbase,
)
ml = (
    augmenters.Xform,
    augmenters.Knn,
    augmenters.SBert_EMB02xlm,
)
mlorg = (
    augmenters.Mlorg,
)

geo = (
    augmenters.GeoRegion,
)

vendors_single = (
    augmenters_vendors.AugmenterClerkFetchAndParse,
)

vendors_multi = (
    #augmenters_vendors.GaiaClerkFetchAndParseMulti,
    curried_constructor(augmenters_vendors.AugmenterClerkFetchAndParseMulti, prefix=CACHE_PREFIX),
)

babyfetch = (
    augmenters_web.FetchAndParsePlainText,
)




def custom_encoder(obj):
    if isinstance(obj, datetime.date):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)  # or str(obj) if you want to keep exact precision
    raise TypeError("Object of type '%s' is not JSON serializable" % type(obj).__name__)


def get_augmenters_by_group(group: str) -> List:
   #  --- get augmenter(s) ---
    if '.' in group:
        subgroup = group.split('.')[1]
        augmenters = globals()[group.split('.')[0]]
        augmenters = [a for a in augmenters if a().formatted_name == subgroup]
    else:
        #  convert str arg to variable
        augmenters = globals()[group]
    return augmenters


def flatten_dict(dd, seperator ='.', prefix = ''):
    #df = pd.json_normalize(dd)
    #dd =  df.to_dict()

    flattened_dict = { prefix + seperator + k if prefix else k : v
             for kk, vv in dd.items()
             for k, v in flatten_dict(vv, seperator, kk).items()
             } if isinstance(dd, dict) else { prefix : dd }

    #  hack for on element lists where element is dict
    res_dict = {}
    for k, v in flattened_dict.items():
        if isinstance(v, list) and len(v) == 1 and isinstance(v[0], dict):
            for ki, vi in flattened_dict[k][0].items():
                res_dict[f'{k}{seperator}{ki}'] = vi
        else:
            res_dict[k] = v

    return res_dict


def concat_description(row, include_name=True):
    if include_name is False:
        doc = str(row.get("short_description") or '') + "\n" + \
              str(row.get("description") or '') + "\n " + \
              str(row.get("keywords") or '')
        return doc.strip()
    #  default is name only
    if row.get("keywords") is None:
        #print('==== No keywords! ====')
        pass
    if row.get("short_description") is None and row.get("description") is None and row.get("keywords") is None:
        return str(row["name"])

    try:
        doc = str(row.get("name") or '') + "\n" + \
              str(row.get("short_description") or '') + "\n" + \
              str(row.get("description") or '') + "\n " + \
              str(row.get("keywords") or '')
    except Exception as e:
        import pdb; pdb.set_trace()
    return doc.strip()


def get_pq_writer(dir_path: str) -> PandasParquetWriter:
    print(f'\n*** parquet file path: {dir_path}\n')
    #return PandasParquetWriter(folder=dir_path, N=20)
    return PandasParquetWriter(folder=dir_path, N=100000)

def get_vector_writer(dir_path: str = None):
    path = '/var/lib/gaia/GAIA_FS/omnibuilder_pipeline/run_0000/001_base_ob_out/sbert_vector/'
    print('VECTOR PATH ===> ', path)
    return keyvector.KeyValueStore_vec_torch(folder_path='/var/lib/gaia/GAIA_FS/omnibuilder_pipeline/run_0000/001_base_ob_out/sbert_vector/')


def get_csv_writer(dir_path: str) -> PandasCSVWriter:
    return PandasCSVWriter(folder=dir_path, N=20000)


def get_kv_writer(file_path: str, reset_cache: bool = False) -> KeyValueStore_sqlite3:
    print(f'GET KV WRITER:  kv file path: {file_path}\n')
    kv = KeyValueStore_sqlite3(file_path=file_path)
    kv.set_mode(mode="safe_tortoise", cursor=None)
    if reset_cache:
        print('resetting kv cache...')
        kv.reset(delete=True)
    return kv

def get_incremental_numpy_writer(dir_path: str = '/var/lib/gaia/GAIA_FS/omnibuilder_pipeline/run_0000/001_base_ob_out/sbert_vector/np/', N=200000):
    return NumpyArrayWriter(folder=dir_path)


# -----------------------------------------------------------------------------------
import multiprocessing


def omni_augmenter_worker(queue, src_path, augmenters, dest_path, reset_cache):
    """Worker for processing (augmenting) batrch of records."""

    omni_augmenter = OmniRecordAugmenter(
        output_prefix=src_path.split('/')[-1].split('.')[0],
        augmenters=augmenters,
        cache_path=dest_path,
        reset_cache=reset_cache
    )

    EMPTY_COUNT_LIMIT = 3
    empty_counts = 0

    time.sleep(30)

    while True:
        try:
            batch = queue.get(block=True, timeout=60)
            if batch == 'DIE':
                break
            #  process batch
            res = omni_augmenter.process_batch(batch)

        except queue.Empty as e:
            print("Empty Exception queue : Empty")
            empty_counts +=1
            time.sleep(60)

        except Exception as e:
            print("[Exception] omni_queue_worker",e)
            raise

        if empty_counts >= EMPTY_COUNT_LIMIT:
            return

    print('WORKER DONE!')
    return True


def launch_omni_augmenter_workers(src_path, augmenters, dest_path, reset_cache):
    max_procs = 16
    workers = []
    queue = multiprocessing.Queue(maxsize=10)
    for i in range(0, max_procs):
        write_proc = multiprocessing.Process(target=omni_augmenter_worker, args=(queue, src_path, augmenters, dest_path, reset_cache))
        write_proc.start()
        workers.append(write_proc)
    return queue, workers


class OmniRecordAugmenter():

    set_kv = True
    set_pq = False
    set_opensearch = False
    set_vector = True
    augmenters_config = {}

    def __init__(self, augmenters: List, cache_path: str = None, output_prefix: str = None, reset_cache: bool = False):
         self.output_prefix = output_prefix
         self.cache_path = cache_path

         if self.cache_path:
             self.build_cache_path = os.path.join(cache_path, 'build')
         else:
             print('No cache path...')
             self.set_kv = False
             self.set_pq = False

         self.augmenters = augmenters
         self._configure_augmenters(reset_cache)

         if self.set_opensearch:
             self.elastico = GaiaElastico()
             self.opensearch_cluster_alias = 'opensearch_worker_fast' #  get dynamically
             self.opensearch_index_alias = ''
             self.opensearch_index_name = ''

    def _configure_augmenters(self, reset_cache=False):

        exclude_from_pq = ('investor', 'dealer', 'people', 'rounds', 'agbase',)

        for augmenter in self.augmenters:
            dd = {}
            cls = augmenter()
            dd['cls'] = cls

            if self.set_kv:
                path = os.path.join(self.build_cache_path, 'kv', f'{cls.formatted_name}.db')
                dd['kv'] = get_kv_writer(path, reset_cache)

            #  some augmenters don't work for pq frames
            if self.set_pq and augmenter.formatted_name not in exclude_from_pq:
                path = os.path.join(self.build_cache_path, 'parquet', f'{cls.formatted_name}_{self.output_prefix}')
                dd['pq'] = get_pq_writer(path)

            #  vector store
            if self.set_kv:
                dd['kv_vector'] = get_vector_writer()
                dd['numpy_writer'] = get_incremental_numpy_writer()

            if self.set_opensearch:
                elastico.connect(alias=self.opensearch_cluster_alias)
            self.augmenters_config[cls.formatted_name] = dd
            print(f'augmenter props set for { cls.formatted_name }')


    def _close_kvs(self):
        print('closing kvs....')
        for dd in self.augmenters_config.values():
            dd['kv'].close()


    def _close_pqs(self):
        print('closing pqs....')
        for dd in self.augmenters_config.values():
            dd['pq'].close()

    def _close_vectors(self):
        print('closing vectors....')
        for dd in self.augmenters_config.values():
            dd['kv_vector'].close()
            dd['numpy_writer'].close()



    def opensearch_incremental_writer(self, raw_record=None):
        """!Not currently used."""

        N = 10000  #  array length for insertion

        #  prep record
        rec = {'_source': raw_record}
        rec['_type'] = '_doc'
        rec['_index'] = self.opensearch_idx
        rec['_id'] = raw_record['goid']

        if self.opensearch_records and len(self.opensearch_records):
             self.opensearch_records.append(rec)
        else:
             self.opensearch_records = [rec]
        if len(self.opensearch_records) >= N:
            #  insert
            self.elastico.insert_bulk(self.opensearch_records, multi=True)
            #  empty queue
            self.opensearch_records = []


    def process_batch(self, batch=None):
        parse_uuid_to_goid = False

        #  input data
        data_dict = {}

        print('process batch len :', len(batch))

        counted = 0
        seen = []
        for row in batch:
            #  Legacy: only if source is CrunchBase
            if parse_uuid_to_goid:
                cbuuid = row['uuid']
                goid = build_goid(src='cbuuid', oid_type='org', srckey=cbuuid, dominant=False)

            else:
                goid = row['GOID']

            #  this data dict is passed to all augmenters
            #  TODO: HACK
            if self.set_vector:
                concat_descr = concat_description(row, include_name=False)
            else:
                concat_descr = concat_description(row)

            data_dict[goid] = {
                'concat_description': concat_description(row),
                'homepage_url': row.get('homepage_url'),
                'source': row
            }

            counted += 1

        print('counted: ', counted)
        print('data_dict len: ', len(data_dict.keys()))
        #  dict keyed by goid
        output_dict = {}

        #  iterate augmenters
        for key, props in self.augmenters_config.items():
            augmenter = props['cls']
            print(f'.............................................augment batch. { augmenter.formatted_name} ')
            #  augment and cache records

            try:
                augmented_dict = self.augment_and_cache_records(augmenter, data_dict)
            except Exception as e:
                print('++++++++++++++++++++ AUGMENT AND CACHE ERROR', e)
                import traceback
                tb = traceback.format_exc()
                print('traceback', tb)
                import pdb; pdb.set_trace()

            for goid, value in augmented_dict.items():
                try:
                     output_dict[goid][augmenter.formatted_name] = value
                except KeyError as e:
                     output_dict[goid] = {augmenter.formatted_name: value}

        #  insert into opensearch [only to be used if opensearch is to be used as a cache]
        #if self.set_opensearch and self.elastico:
        #    self.opensearch_insert(cache_dict)

        return output_dict


    def augment_and_cache_records(self, augmenter=None, data_dict=None):

        cfg = self.augmenters_config[augmenter.formatted_name]
        kv = cfg.get('kv')
        pq = cfg.get('pq')
        kv_vector = cfg.get('kv_vector')
        incre_np = cfg.get('numpy_writer')

        #  --- augment ---
        print('data dict in', len(data_dict.keys()))
        augmented_dict = augmenter.run(data_dict)
        print('augment_and_cache_records: augmented dict', len(augmented_dict.keys()))

        #  sanity check
        if len(augmented_dict.keys()) != len(data_dict.keys()):
            print('augmented length mismatch.....')
            #import pdb; pdb.set_trace()

        #  --- cache ---
        #  iterate augmented batch

        print(f'set cache... KV: {self.set_kv}, PQ: {self.set_pq}')

        for goid, value in augmented_dict.items():
            if self.set_kv and kv:
                print('set kv........', goid)
                kv.set(goid, value)

            if self.set_pq and pq:
                #  format is goid
                row = {'goid': goid}
                #  here we are flattening dict
                #  so that each key column is a key
                preds = flatten_dict(value)
                row.update(preds)
                #print('ROW =======> ', row)
                # TODO: fix this hack!
                #if isinstance(row.get('content.descr.sum_lines'), list):
                #    row['content.descr.sum_lines'] = row['content.descr.sum_lines'][0]
                pq.accept(row)

            if self.set_vector and kv_vector:
                #  store vectors KeyValueStore_vec_torch
                np_arr = np.array(value)
                kv_vector.set(goid, np_arr)
                #  incremental numpy
                incre_np.accept(np_arr)

        return augmented_dict


    def post_build(self):
        if self.set_kv:
            self._close_kvs()

        if self.set_pq:
            self._close_pqs()

        if self.set_vector:
            self._close_vectors()

        for key, dd in self.augmenters_config.items():
            augmenter = dd['cls']

            #  mv cache from /build
            self.update_augmenter_path(augmenter.formatted_name)

            """
            if self.set_pq:
                #  get augmenter pq instance
                pq = dd['pq']
                pq_dir = os.path.dirname(pq._get_path())
                dest_dir = pq_dir.replace(augmenter.formatted_name, 'compressed')
                print('pq_dir, dest_dir', pq_dir, dest_dir)
                current_time = datetime.now()
                timestamp_str = current_time.strftime("%Y%m%d%H%M%S")
                #  compress pq_dir
                output_file = os.path.join(dest_dir, f'{augmenter.formatted_name}_{timestamp_str}.tar.gz')
                print('compressing to...', output_file)
                with tarfile.open(output_file, "w:gz") as tar:
                    tar.add(pq_dir, arcname="")
            """


    def update_augmenter_path(self, augmenter_formatted_name: str) -> str:
        """Move cache from build location to cache location."""

        for cache in ['pq', 'kv']:
            src_path = None
            dest_path = None

            if self.set_pq and cache == 'pq':

                #  PQ
                filename = f'{augmenter_formatted_name}_{self.output_prefix}'
                src_path = os.path.join(self.build_cache_path, 'parquet', filename)
                #dest_path = os.path.join(self.cache_path, 'parquet', filename)
                dest_path = os.path.join(self.cache_path, 'test', filename)


            if self.set_kv and cache == 'kv':

                #  KV
                src_path = os.path.join(self.build_cache_path, 'kv', f'{augmenter_formatted_name}.db')
                dest_path = os.path.join(self.cache_path, 'kv', f'{augmenter_formatted_name}.db')

                src_kv = KeyValueStore_sqlite3(file_path=src_path)
                dest_kv = KeyValueStore_sqlite3(file_path=dest_path)

                #  count rows (fast method)
                print(f'kv counts: src: {src_kv.count_fastupperlim()}, dest: {dest_kv.count_fastupperlim()}')

            if not src_path or not dest_path:
                continue

            if not os.path.exists(src_path):
                print('no augmenter cache! ... nothing written?')
                continue

            #  move to destination if destination is absent or destination is smaller than build
            #  TODO: for GaiaDescribe let's append to cache and keep
            if not os.path.exists(dest_path) or os.path.getsize(src_path) >= os.path.getsize(dest_path):
                print('/////////////////////////////////////////////// moving to dest path...', dest_path)
                print('src_path', src_path)

                #  for dirs of files
                if os.path.exists(dest_path) and os.path.isdir(dest_path):
                    #  it's a dir!
                    shutil.rmtree(dest_path)
                    shutil.copytree(src_path, dest_path)

                if os.path.exists(dest_path) and not os.path.isdir(dest_path):
                    #  it's a file!
                    #shutil.move(src_path, dest_path)
                    shutil.copyfile(src_path, dest_path)

                if 1:
                    #print('pausing for 10...')
                    #time.sleep(10)
                    print('converting to pq...')
                    pq_dir = dest_path.replace('/kv', '/pq').replace('.db', '/')
                    if not os.path.exists(pq_dir):
                        os.mkdir(pq_dir)
                    #pq_path = os.path.join(pq_dir, 'frame.pq')
                    kv2pq(src_path=dest_path, dest_path=pq_dir)
            else:
                print('/////////////////////////////////////////////// src_path is smaller than dest_path so will not move!', [src_path, dest_path])


    def opensearch_insert(self, augmented_dict=None):
        #  list of all records
        #  with opensearch metadata
        augmented_records = [
            {
                '_type': '_doc',
                '_index': self.opensearch_index_name,
                '_id': goid,
                '_source': {'goid': goid, **dd}
            }
            for goid, dd in augmented_dict.items()
        ]
        #if self.set_opensearch:
        #    opensearch_incremental_writer(self, raw_record=augmented_rec)

        #  send whole batch to opensearch
        self.elastico.insert_bulks(augmented_records)

        return augmented_records







@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-g', '--group', type=str, default=None)
@click.option('-s', '--src_path', type=str, default=None)
@click.option('-d', '--dest_path', type=str, default=None)
@click.option('-o', '--offset', type=int, default=0)
@click.option('-m', '--multiprocess', is_flag=True, default=False)
@click.option('-i', '--increment_cache', is_flag=True, default=False)
def run(group=None, batch_amt=None, hard_limit=None, full_update=False, src_path=None, dest_path=None, offset=None, multiprocess=False, increment_cache=False):

    print(f'local kwargs: {locals()}')

    MULTIPROCESS = multiprocess
    start_row = offset

    if increment_cache is True:
        reset_cache = False
    else:
        reset_cache = True


    print(f'MULTIPROCESS: {MULTIPROCESS}')

    #  generator from csv file
    #src_csv_path = '/home/<USER>/agbase_admin_min37_dj200/DATA/cb_dnld_20230721/organizations.csv'
    #src_gen = csv_generator(file_path=src_csv_path)

    #  TODO: debug
    df = pd.read_parquet(src_path)
    print('LEN ========================================================= ', len(df))


    if src_path is None:
        raise ValueError('no source supplied!')
        #src_path = '/var/lib/gaia/GAIA_FS/frames/omni/org_merge/frame.parquet'

    if src_path and src_path.split('.')[1] not in ('pq', 'parquet'):
        raise NotImplemntedError('only parquet sources supported!')

    #  create generator from source (batches of batch_amt)
    if start_row:
        src_gen = parquet_generator_slice(
            file_path=src_path,
            batch_size=batch_amt,
            start_row=start_row
        )
    else:
        print('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> vanilla parquet gen')
        src_gen = parquet_generator(file_path=src_path, batch_size=batch_amt)
        ###src_gen = parquet_generator_sample(file_path=src_path, batch_size=batch_amt)


    augmenters = get_augmenters_by_group(group)

    if len(augmenters) < 1:
        raise ValueError('no augmenters registered!')

    if MULTIPROCESS:
        # --- instantiate queue, workers ---
        queue, workers = launch_omni_augmenter_workers(
            src_path, augmenters, dest_path, reset_cache
        )
    else:
        # --- instantiate omni augmenter ---
        omni_augmenter = OmniRecordAugmenter(
            output_prefix=src_path.split('/')[-1].split('.')[0],
            augmenters=augmenters,
            cache_path=dest_path,
            reset_cache=reset_cache
        )



    #  hack for now ----------
    if 'SBert_EMB02xlm' not in str(augmenters):
        print('sbert not in run, unsetting kv_vector...')
        omni_augmenter.set_vector = False
    # ------



    count = 0
    if start_row:
        print(f'''
            ======================
            START ROW: {start_row}
            ======================
            '''
        )
        count += start_row
        #if hard_limit:
        #    hard_limit += start_row
    print(f'START: count: {count}, hard_limit: {hard_limit}')

    #  iterate source
    batch_count = 0
    for batch in batch_generator(src=src_gen, batch_size=batch_amt):
        print(f'batch count: {batch_count},  batch len: {len(batch)}')
        batch_count += 1
        #print('augment and cache batch...')
        if MULTIPROCESS:
            queue.put(batch)
        else:
            omni_augmenter.process_batch(batch)

        #  increment count
        count += len(batch)
        print(f'...........................................................................................................count: { count }')

        #  *** TERMINATE ***: end of source data
        if len(batch) < batch_amt:
            print('no more records.')
            break

        #  *** TERMINATE ***: hard_limit reached
        if hard_limit and count >= hard_limit:
            print(f'hard limit reached. count: {count}, hard_limit: {hard_limit}')
            break

    #  post build steps
    if MULTIPROCESS:
        [queue.put('DIE') for p in workers]
        [p.join() for p in workers]
        #  we need an instance of OmniRecordAugmenter for post build steps
        omni_augmenter = OmniRecordAugmenter(
            output_prefix=src_path.split('/')[-1].split('.')[0],
            augmenters=augmenters,
            cache_path=dest_path,
            reset_cache=reset_cache
        )

    #  cleanup
    omni_augmenter.post_build()
    print('done!')

if __name__ == '__main__':
    run()
