import csv
from ftlangdetect import detect
from gaia.core.gaia_goid import build_goid
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico



sample_uuids = ['*************-2ee5-2253-8aa3f6fcadb4']


english_sample = "AgFunder invests in impactful companies that can positively transform the world's food system. Founded in 2013, AgFunder manages a portfolio of over 50 companies across four funds from offices in Silicon Valley and Singapore. Through its media platform AFN, it has built a global ecosystem of over 90,000 subscribers."


def run():
    ES_INDEX = 'idx_agsearch_worker_fast2'
    elastico = GaiaElastico()
    elastico.connect(alias='opensearch_worker_fast')

    if 1:
        #  write headers to csv
        cols = ['uuid', 'company name', 'sample text', 'langdetect lang', 'langdetect_score']
        with open('langdetect.csv', 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(cols)

    for uuid in sample_uuids:
        #  query source
        res = elastico.get_record_by_id(ES_INDEX, uuid)
        #  lasngdetect on description
        plaintext = res[0]['_source'].get('plaintext')

        if 1:
            plaintext += english_sample

        company_name = res[0]['_source']['company_name']
        lang = detect(text=plaintext.replace('\n', ''), low_memory=False)

        #  write to csv
        with open('langdetect.csv', 'a', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow([uuid, plaintext, lan['lang'], lang['score']])

if __name__ == '__main__':
    run()

