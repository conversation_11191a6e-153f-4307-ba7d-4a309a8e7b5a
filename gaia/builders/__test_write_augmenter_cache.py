import os
import sys
import uuid
import json
import time

parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)

#  es connect
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico

#  key value store
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.util.keyvalue.keyvalue import KeyValueStore_shelve

#  local
from gaia.gaia_fs.paths import gfs_folder_local
from gaia.builders import augmenters


CACHE_PATH = gfs_folder_local(tree='gfs_datasets')


def run(kv=None, data={}):
    aug = augmenters.Dealer()
    for key, value in data.items():
        kv.set(key, value)


if __name__ == '__main__':
    if 0:
        kv = KeyValueStore_sqlite3(file_path=CACHE_PATH + '{}.db'.format('pref_test'))
        aug = augmenters.Dealer()
        data_dict = {'uuids_doc_dict': {'12082774-e32f-43f4-9a9f-1a2cb14569bc': ''}}
        data = aug.compute(data_dict=data_dict)
        run(kv=kv, data=data)
    if 0:
        times = {}
        kvs = (
            KeyValueStore_sqlite3(file_path=CACHE_PATH + '{}.db'.format('sqlite3_perf_test')),
            KeyValueStore_shelve(file_path=CACHE_PATH + '{}'.format('shelve_perf_test')))
        for kv in kvs:
            print('set kv...', kv)
            count = 0
            ts = time.time()
            while True:
                data = {
                    str(uuid.uuid4()): {
                        "incl_agrifood_tech": 0.0074310302734375,
                        "incl_agrifood": 0.0292510986328125,
                        "edu_rank_times1": 4,
                        "edu_rank_empirical1": 2,
                        "edu_rank_nature1": 5,
                        "max_inv_comp_count": 319,
                        "max_inv_acq_count": 87,
                        "max_inv_ipo_count": 85,
                        "max_hitrate_acq": 0.0,
                        "max_hitrate_ipo": 0.0,
                        "max_inv_round_median": 60000000.0,
                        "max_inv_round_sum": 83570907169.0,
                        "degree_phd_count": 0,
                        "ppl_count": 5,
                        "edu_rank_geo_country_empirical1": 2,
                        "edu_rank_geo_subregion_empirical1": 2,
                        "edu_rank_geo_region_empirical1": 2,
                        "degree_grad_count": 0,
                        "degree_grad_any": 0,
                        "degree_master_any": 0,
                        "degree_master_count": 0,
                        "degree_phd_any": 0,
                        "pastjobs_funding": 0.0,
                        "score_early": 0.191199854047405
                   }
                }
                #print('set...', data)
                if count % 10000 == 0:
                    print('count: ', [count, list(data.keys())[0]])
                run(kv=kv, data=data)
                if count >= 1000000:
                    break
                count += 1
            te = time.time()
            times[kv.__class__.__name__] = te-ts
        print('time delta: ', times)
    if 1:
        kvs = (
            KeyValueStore_sqlite3(file_path=CACHE_PATH + '{}.db'.format('sqlite3_perf_test')),
            KeyValueStore_shelve(file_path=CACHE_PATH + '{}'.format('shelve_perf_test')))
        for kv in kvs:
            print('get kv...', kv)
            uuid = 'd6ec137f-1239-4857-a755-fcea343820a0'
            ts = time.time()
            kv.get(key=uuid)
            te = time.time()
            print(f'time delta: {te-ts}')
