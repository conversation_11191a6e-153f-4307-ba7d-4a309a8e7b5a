import click
import requests
from pprint import pprint as pp
from bs4 import BeautifulSoup
from typing import Dict
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico


class GaiaFinder:

    elastico = GaiaElastico()

    def get_company_by_name(self, org_name: str = None):
        results = {}
        totals = {}

        '''
        cb_web = self.__get_cb_org(org_name=org_name)
        results['cb_web'] = [cb_web]
        totals['cb_web'] = len(results['cb_web'])
        '''

        es = self.__get_es_org(org_name=org_name)
        results['es'] = es
        for idx in results['es'].keys():
            totals[idx] = len(results['es'][idx])

        totals = dict(sorted(totals.items()))
        return totals, results

    @staticmethod
    def __package_es_org(src: Dict, idx: str) -> Dict:
        rr = {}
        rr['uuid'] = src.get('uuid', 'n/a')
        if 'agsearch' in idx:
            rr['gaia_url'] = f"https://agbase.agfunder.com/search/{idx}/{rr['uuid']}"
        rr['name'] = src.get('company_name') or src.get('name', 'no key')
        rr['url'] = src['homepage_url']
        rr['description'] = src['short_description']
        rr['created'] = src.get('created_at', 'n/a')

        if src.get('xform_preds'):
            rr['agrifood'] = src['xform_preds']['incl_flag']
            rr['agrifood_tech'] = src['xform_preds']['incl_strict']
        return rr


    @staticmethod
    def __get_cb_org(org_name=None) -> Dict:
        data = {}
        agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36'
        headers = {'User-Agent': agent}
        url = f"https://www.crunchbase.com/organization/{ org_name.lower().replace(' ', '-') }"
        print(f'cb_org url: {url}')
        res = requests.get(url, headers=headers)
        soup = BeautifulSoup(res.text, "lxml")
        #titles = soup.find_all("h2", class_="section-title", string='About')[0]
        about = soup.find_all('span', class_='description')[0].text
        data['name'] = org_name
        data['description'] = about
        data['url'] = url
        return data

    def __get_es_org(self, org_name=None) -> Dict:
        #  get all indexes:
        self.elastico.connect(alias='opensearch_worker')
        print('details', self.elastico.details)
        idxs = self.elastico.list_indexes()
        data = {}
        for idx in idxs:
            print(f'query index: { idx }')
            res = self.elastico.docs_query(
                index=idx,
                field_names=['company_name^0.5','alias1^1','description^0.25','short_description^0.25'],
                query_string=org_name.lower().replace(' ', '+'),
                pp_count=5
            )
            #  return list of results per index
            data[idx] = []
            if len(res) > 1:
                for r in res[0]['hits']['hits']:
                    src = r['_source']
                    print('hit: ', src['homepage_url'])
                    if org_name.lower() not in src.get('short_description', '').lower() \
                        and org_name.lower() not in src.get('name', '').lower():
                        continue
                    try:
                        rr = self.__package_es_org(src=src, idx=idx)
                        dd = self.elastico.details
                        rr['es_url'] = f"http://{dd['endpoint']}:{dd['port']}/{idx}/_search?pretty=true&q=_id:{rr['uuid']}"
                        data[idx].append(rr)
                    except Exception as e:
                        print(f'could not package es result for index: {idx}', e)
        return data


@click.command()
@click.option('-o', '--org', type=str, default=None)
def find_org(org=None):
    #from viztracer import VizTracer
    #tracer = VizTracer()
    #tracer.start()

    print(f'query: {org} | Searching...')
    gf = GaiaFinder()
    totals, results = gf.get_company_by_name(org_name=org)
    print('\nHITS:')
    pp(totals)
    print('\nDETAILS:')
    pp(results)

    #tracer.stop()
    #tracer.save()


if __name__ == '__main__':
    find_org()
