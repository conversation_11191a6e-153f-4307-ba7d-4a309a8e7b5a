from gaia.gaia_elasticsearch import gaia_elasticsearch
from gaia.gaia_ml.util import *
import rawsql.sql
import pandas as pd
from pdb import set_trace as st
from gaia.gaia_fs.paths import GAIA_ROOT
from gaia.gaia_ml import names
import gen_exporter
from sklearn import preprocessing
import pickle

# this JUST provides the tags
# need to join with other things to get the rest

class AgbaseTagExporter( gen_exporter.PostgresFrameExporter ):

    config_task_slug = 'org_descr2tags'
    config_mainframe_pq_fn = 'cb_uuid_agbase_tagslugs.pq'

    def _get_query(self):
        q = '''

            -- table of uuid -> tag_slugs, INCLUDING many negative examples (no tags)


            -- positive examples
            (
                select
                    es_id as uuid,
                    ( select string_agg( tag_slug,' ') ) as tag_slugs
                from
                (
                    select
                        agsearch_ascontext.context_type||'_'||
                        lower( replace(
                            replace( agsearch_ascontext.name::text,'TAG__'::text,''),
                            ' ','_'
                            )) as tag_slug,
                    es_id,
                    rating
                        from agsearch_asrating left join agsearch_ascontext
                            on agsearch_asrating.context_id=agsearch_ascontext.id

                            -- ONLY do the TAG__ contexts
                            where ( -- agsearch_ascontext.name ilike 'TAG__%' and
                              agsearch_ascontext.context_type in ('tag','compete','client')  )
                               and
                               agsearch_asrating.rating=1
                ) aa
                left join cb16_organizations on cb16_organizations.uuid=aa.es_id
                group by es_id
            )
            union -- 1

            /*
            -- add negative examples
            -- HOWEVER, these are biased in that they are:
            -- 1) in agbase at all
            -- 2) prose, non-web text
            -- 3) company description text
            -- 4) paragraph-length

            Do we want to classify these too?  Could help with OOD detection at very least.

            */
            (
                select cb_uuid, '' as tag_slugs  from agbase_org where org_role='COMP' and org_class='ORG' and
                rec_inclusion='N' and cb_uuid is not null
                limit 10000
            )
            /*
            -- above could rather be: load a bunch of random CB companies that are not in agbase ,
            -- with:
            datasource_cb       #  datasource_webtext, datasource_pb, ...
            entity_org_comp     #  entity_org_inv, entity_person, entity_none
            agbase_absent       #  agbase_agrifood agbase_agrifoodtech agbase_stream_up agbase_stream_down

            agbase_cat_asd-das-wer  # category tags?

            */
            union --2
            (
                -- grab a bunch of companies that are NOT in ratings at all
                select uuid, '' as tag_slugs  from cb16_organizations where
                    uuid not in (  select
                                  es_id
                            from agsearch_asrating left join agsearch_ascontext
                                on agsearch_asrating.context_id=agsearch_ascontext.id
                                where agsearch_ascontext.name ilike 'TAG__%' and
                                  agsearch_ascontext.context_type='tag'
                    )   limit 10000
            )
        '''
        return q


    def _export_subframes( self, mainframe, folder ):
        print(mainframe.columns)
        print(mainframe)
        # return
        cols = ['uuid','tag_slugs']
        dest_fn_pq = folder + '/export__descr__tags.pq'
        print("Writing", dest_fn_pq)
        mainframe[cols].to_parquet(dest_fn_pq,index=False)

        pp_mlb_agbase_tags = preprocessing.MultiLabelBinarizer()
        pp_mlb_agbase_tags.fit(mainframe['tag_slugs'].str.split(' ').to_list() )
        print(pp_mlb_agbase_tags.classes_)
        # this can be done later:
        # mlb = mlproc.transform(mainframe['ext_tags'].str.split(' ').to_list())
        # print(mlb)
        # save MultiLabelBinarizer
        folder=self.dest_folder

        fn = folder + '/skl.preproc.mlb_agbase_tags.pickle'
        with open(fn, 'wb') as handle:
            print("writing", fn)
            pickle.dump(pp_mlb_agbase_tags, handle, protocol=pickle.HIGHEST_PROTOCOL)

        # load MultiLabelBinarizer
        with open(fn, 'rb') as handle:
            pp_mlb_agbase_tags = pickle.load(handle)





if __name__=="__main__":
    #mode="CACHED"
    mode="REFRESH"
    ex=AgbaseTagExporter()
    ex.main(cache_mode=mode, test_mode=False)

