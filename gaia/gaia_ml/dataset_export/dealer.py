from gaia.gaia_elasticsearch import gaia_elasticsearch
from gaia.gaia_ml.util import *
import rawsql.sql
import pandas as pd
from pdb import set_trace as st
from gaia.gaia_fs.paths import GAIA_ROOT
from gaia.gaia_ml import names
from gaia.gaia_ml.dataset_export.gen_exporter import PostgresFrameExporter


class DealerFrameExporter( PostgresFrameExporter ):

    def _get_query( self ):

        q = """
    select cbo.uuid,cbo.company_name,
    cbo.short_description,
    cod.description,
    dtv.*, dfr.*
        from cb16_organizations cbo
        left join dealer_team_view_base_01 dtv on dtv.uuid = cbo.uuid
        left join dealer_frventure_view_01 dfr on dfr.company_uuid = cbo.uuid
        left join cb16_organization_descriptions cod on cod.uuid = cbo.uuid
        """
        return q



if __name__=="__main__":
    a = DealerFrameExporter()
    a.config_task_slug = 'org_descr2dealer'
    a.config_mainframe_pq_fn = 'cb_uuid__org_descr2dealer.pq'

    print( a )
    a.main( cache_mode="REFRESH", test_mode=False)

    # a.main( cache_mode="CACHED", test_mode=True)
