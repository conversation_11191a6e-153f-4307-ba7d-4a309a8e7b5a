import sys
import yaml
import gaia.babyspider.babyparse
from gaia.gaia_elasticsearch import gaia_elasticsearch
from gaia.gaia_ml.util import *
import rawsql.sql
import pandas as pd
from pdb import set_trace as st
from gaia.gaia_fs.paths import GAIA_ROOT
from gaia.gaia_ml import names

from gaia.gaia_ml.dataset_export import gen_exporter


'''

Initial Query:  4.5 GB

Peak memory usage: 24 GB

'''

class CBCatExporter( gen_exporter.PostgresFrameExporter ):

    config_task_slug = 'org_descr2cbcats'
    config_mainframe_pq_fn = 'export_cbcats_mainframe.pq'

    def _get_query(self):
        q = '''
    select
        mcbo.*,
        cat,cat_stream,
        agbo.rec_inclusion as incl,
        mcbo.created_at,

        dv.geo_continent,
        dv.geo_sub_continent,

        dv.max_inv_comp_count,

        dv.max_hitrate_acq,
        dv.max_hitrate_ipo,
        dv.max_inv_comp_count,
        dv.max_inv_round_sum,
        dv.max_inv_comp_count,

        dv.all_investors,

        dv.degree_phd_count,
        dv.degree_phd_any,
        dv.degree_grad_count,
        dv.degree_grad_any,
        dv.edu_rank_empirical1,
        dv.edu_rank_times1,
        dv.edu_rank_nature1,
        dv.edu_rank_nat_repute1,
        dv.pastjobs_funding,

        dv.num_exits,

        dv.score_early,
        dv.score_early_founded,
        dv.score_early_empl,
        dv.score_early_raised_rounds,
        dv.score_early_raised_amt

    -- mcbo.uuid, mcbo.company_name, mcbo.slug, descr_text, '' as webtext, cat, rec_inclusion as incl,
     -- category_list, category_group_list
     from
        (
            select cbo.uuid,
                cbo.company_name, coalesce(short_description,'')||' '|| coalesce(description,'') as descr_text,
            cbo.permalink as slug,
            cbo.created_at,
            cbo.linkedin_url,
            cbo.twitter_url,
            cbo.homepage_url,
            cbo.country_code,

            funding_total_usd,
            funding_rounds,
            cbo.founded_on,
            last_funding_on,

            cbo.primary_role,
            cbo.status,
            cbo.rank as cb_company_rank,

            num_exits,
            category_list, category_group_list,

            cb16_investors.investor_type,
            cb16_investors.investment_count,
            cb16_investors.investor_type,
            cb16_investors.rank as cb_investor_rank

            from cb16_organizations cbo
                left join cb16_organization_descriptions cbod  on  cbod.uuid=cbo.uuid
                left join cb16_investors on cb16_investors.uuid=cbo.uuid
        ) mcbo

    left join
        (
            select agborg.cb_uuid as uuid,
            agborg.rec_inclusion as rec_inclusion,
            agcat.slug as cat,
            agcat.stream as cat_stream
            from agbase_org agborg left join agbase_agtechcategory agcat  on  agborg.category_id=agcat.id
        ) agbo
        on agbo.uuid =  mcbo.uuid

        left join
        dealer_view_base_01 dv
        on dv.uuid = mcbo.uuid
        -- DANGER: this is circular!
        -- maybe what we really want is:
        --  - team data (phds, edurank)
        --  - pastjobs_funding
        --  - schools , pastjobs [ text ]
        --  - max_inv_acq_count
        --  - geo_continent, geo_sub_continent
        --

        -- left join dealer_team_view_base_01 dtv on dtv.uuid = mcbo.uuid
        --left join dealer_frventure_view_01 dfr on dfr.company_uuid = mcbo.uuid


    -- limit 100000
        '''
        return q


    def _dataframe_refine( self, df,  ):

        if 0:
            df['webtext'] = ''
            for cbuuid in df['uuid'].tolist():
                print('Looking for ', cbuuid)
                single_webdoc = get_webtext_for_cbuuid(ec, cbuuid)
                if single_webdoc:
                    print('found webtext for', cbuuid)
                    df.loc[df.uuid == cbuuid, 'webtext'] = single_webdoc

        df = cleantext_col(df, 'descr_text')
        if 'webtext' in df:
            df = cleantext_col(df, 'webtext')

        # in agbase ML format
        dfc = df.copy() #[['company_name', 'uuid', 'cat', 'incl', 'slug', 'category_list', 'category_group_list', 'webtext','descr_text']]

        dfc['domain'] = dfc.apply(lambda row: gaia.babyspider.babyparse.url_to_domain(row['homepage_url']), axis=1)

        dfc['ext_tags'] = dfc.apply(lambda row: slugize_cb_group_strings(row), axis=1)

        dfc['cb_uuid'] = dfc['uuid']

        dfc['primary_role_tag'] = 'ext_cb_role_'+ dfc['primary_role'].astype(str).str.lower()


        # convert category and inclusion into tags
        dfc['tag_cat'] = 'agbase_cat_' + dfc['cat'].astype(str).str.lower()
        # dfc['tag_cat'] = dfc['tag_cat'].str.replace('-','.')
        dfc['tag_include'] = 'agbase_incl_absent'
        dfc.loc[(dfc['incl'] == 'Y') & (dfc['cat'] != 'not-in-report'), 'tag_include'] = 'agbase_incl_agrifoodtech'
        dfc.loc[(dfc['incl'] == 'Y') & (dfc['cat'] == 'not-in-report'), 'tag_include'] = 'agbase_incl_agrifood'
        dfc.loc[dfc['incl'] == 'N', 'tag_include'] = 'agbase_incl_exclude'

        #cols = ['cb_uuid', 'company_name', 'slug',  'ext_tags', 'descr_text', 'webtext', 'category_list',
        #        'category_group_list', 'tag_include']
        #dfc = dfc[cols]

        print(df.head())
        print(dfc.head())
        #st()
        return dfc


    def _export_subframes(self, mainframe, folder, test_mode=False):
        cols = ['uuid','company_name','linkedin_url','homepage_url','descr_text','domain','ext_tags']
        dest_fn_pq = folder + '/subframe__indent_descr__cbtags.pq'
        print("Writing", dest_fn_pq)
        mainframe[cols].to_parquet(dest_fn_pq,index=False)
        if test_mode:
            dest_fn_csv = dest_fn_pq.replace(".pq",".csv")
            mainframe[cols].to_parquet(dest_fn_csv, index=False)

        cols = ['uuid','descr_text','ext_tags']
        dest_fn_pq = folder + '/subframe___descr__tags.pq'
        print("Writing", dest_fn_pq)
        mainframe[cols].to_parquet(dest_fn_pq,index=False)
        if test_mode:
            dest_fn_csv = dest_fn_pq.replace(".pq",".csv")
            mainframe[cols].to_parquet(dest_fn_csv, index=False)


        # -- preprocessing
        from sklearn import preprocessing
        import pickle

        # -- preprocessing
        pp_mlb_ext_tags = preprocessing.MultiLabelBinarizer()
        pp_mlb_ext_tags.fit(mainframe['ext_tags'].str.split(' ').to_list() )
        # this can be done later:
        # mlb = mlproc.transform(mainframe['ext_tags'].str.split(' ').to_list())
        # print(mlb)
        # save MultiLabelBinarizer
        folder=self.dest_folder
        fn = folder + '/skl.preproc.mlb_ext_tags.pickle'
        print("Writing",fn)
        with open(fn, 'wb') as handle:
            pickle.dump(pp_mlb_ext_tags, handle, protocol=pickle.HIGHEST_PROTOCOL)
        # load MultiLabelBinarizer
        print("Reading",fn)
        with open(fn, 'rb') as handle:
            pp_mlb_ext_tags = pickle.load(handle)


        if test_mode:
            dest_fn_csv = dest_fn_pq.replace(".pq",".csv")
            mainframe[cols].to_csv(dest_fn_csv, index=False)

        # new_cols = res.columns.to_list()
        dest_fn_pq_catdummycols = folder + '/cbtags_dummies_cols.yaml'
        print("Writing columns list", dest_fn_pq_catdummycols)
        # write list to yaml file
        with open(dest_fn_pq_catdummycols, 'w') as file:
            file.write(yaml.dump(pp_mlb_ext_tags.classes_.tolist()))

if __name__=="__main__":



    usage="""
Command Line Usage
------

# Refresh Export

## Test (small) refresh of sample export data (mostly pq files).
python -m gaia.gaia_ml.dataset_export.agsearch_cbcats TEST_REFRESH

## Full refresh of export data (mostly pq files). WARNING: takes minutes
python -m gaia.gaia_ml.dataset_export.agsearch_cbcats REFRESH


# parquet file management

## view data sample of pq file on screen
python -m gaia.gaia_ml.dataset_export.agsearch_cbcats PQVIEW \
            /var/lib/gaia/GAIA_FS/datasets/edition_prod/Torg_descr2cbcats/subframe___descr__tags.pq

## dump from pq to csv (primarily for debug) -- then you could use eg. tabview
python -m gaia.gaia_ml.dataset_export.agsearch_cbcats PQ2CSV \
            /var/lib/gaia/GAIA_FS/datasets/edition_prod/Torg_descr2cbcats/subframe___descr__tags.pq


"""
    print(usage)

    # mode="REFRESH"
    mode = sys.argv[1]

    # TODO: All this could be boilerplated

    if mode=="TEST_REFRESH":
        #mode="CACHED"
        ex=CBCatExporter()
        ex.main(cache_mode="REFRESH", test_mode=True)

    elif mode=="REFRESH":
        #mode="CACHED"
        ex=CBCatExporter()
        ex.main(cache_mode=mode, test_mode=False)

    print(mode)
    if mode[:2]=="PQ":
        fn=sys.argv[2]
        print(fn)
        df = pd.read_parquet( fn )

        if mode=="PQVIEW":
            print( df.columns)
            print( df.describe().T )
            print( df.head(10) )
        if mode=="PQ2CSV":
            fn2=fn+'.csv'
            print(fn2)
            df.to_csv(fn2)
