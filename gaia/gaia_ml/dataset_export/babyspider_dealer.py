from gaia.gaia_elasticsearch import gaia_elasticsearch
from gaia.gaia_ml.util import *
import rawsql.sql
import pandas as pd
from pdb import set_trace as st
from gaia.gaia_fs.paths import GAIA_ROOT
from gaia.gaia_ml import names
from gaia.gaia_ml.dataset_export.gen_exporter import PostgresFrameExporter, GeneralizedFrameExporter

from gaia.util.keyvalue import keyvalue, mproc_kv




class BabyspiderDealerFrameExporter( GeneralizedFrameExporter ):

    row_dicts=None

    def _add_row(self, row):
        if self.row_dicts is None:
            self.row_dicts = []
        self.row_dicts.append( row )


    def _fetch_raw_df( self, limit = None, test_mode=False):
        df = pd.DataFrame( self.row_dicts )
        return df


if __name__=="__main__":
    a = BabyspiderDealerFrameExporter()
    a.config_task_slug = 'org_webhome2dealer'
    a.config_mainframe_pq_fn = 'cb_uuid__org_webhome2dealer.pq'

    # for k in kv.iterator():
    #   row={}
    #   row[''] = ''
    #   row[''] = ''
    #   a._add_row( row )
    #   these rows will get returned by _fetch_raw_df()

    print( a )
    a.main( cache_mode="REFRESH", test_mode=False)
    # a.main( cache_mode="CACHED", test_mode=True)
