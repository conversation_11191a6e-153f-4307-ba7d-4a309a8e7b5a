from gaia.gaia_elasticsearch import gaia_elasticsearch
from gaia.gaia_ml.util import *
import rawsql.sql
import pandas as pd
from pdb import set_trace as st
from gaia.gaia_fs.paths import GAIA_ROOT
from gaia.gaia_ml import names

from gaia.gaia_ml.dataset_export import agsearch_cbcats

if __name__=="__main__":
    '''
    folder = GAIA_ROOT + 'agbaseml_traindata/' + names.get_task_slug('org_descr2cbcats') + '/'
    fn = folder + 'export_cb_cats.csv'
    print("Reading ", fn)
    df_cats = pd.read_csv(fn)
    print(df_cats.head())
    df_cats=df_cats.set_index('uuid')

    folder = GAIA_ROOT + 'agbaseml_traindata/' + names.get_task_slug('org_tags') + '/'
    fn = folder + 'cb_uuid_agbase_tagslugs.csv'
    print("Reading ", fn)
    df_tags = pd.read_csv(fn)
    print(df_tags.head())
    df_tags=df_tags.set_index('uuid')

    dfm=pd.merge(
        df_cats,
        df_tags,
        how="outer",
        left_index=True,
        right_index=True,
        # suffixes=("_tags", "_cbcats"),
        # copy=True,
        # indicator=False,
        # validate=None,
    )

    folder = GAIA_ROOT + 'agbaseml_traindata/' + names.get_task_slug('org_descr2tags') + '/'

    fn = folder + 'cb_uuid_agbase_tagslugs.csv'
    print("Writing ", fn)
    print(dfm.columns)
    print(dfm.head())
    dfm.to_csv(fn)

    '''
    pass
