import json
import openai
from pdfreader import SimplePDFViewer
from PyPDF2 import PdfReader

import sys
sys.path.append("../..")
from gaia.babyspider.cached_babyfetch import CachedBabyFetch
from gaia.babyspider.babyspider_augmenters import PlainText
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico


ES_INDEX = 'idx_agsearch_worker1'


def query_agsearch(url: str = None, text: str = None, more_like_this: bool = False, query_agsearch: bool = False):
    if query_agsearch:
        record = fetch_es_record(url=url)
        if record.get('uuid'):
            agsearch_url = f"https://agbase.agfunder.com/search/{record['index']}/{record['uuid']}"
            company_name = record.get('company_name')
            text += f'\n---\n<b>AgSearch query:</b> \n'
            text += f"<a href='{agsearch_url}' target='_blank'>{company_name}</a><br />"

    if more_like_this:
        if not query_agsearch:
            record = fetch_es_record(url=url)
        uuid = record.get('uuid')
        more_likes = es_more_like_this(uuid=uuid)
        text += f'\n---\n<b>More Like query:</b> \n'
        if len(more_likes):
            for r in more_likes:
                agsearch_url = f"https://agbase.agfunder.com/search/{r['_index']}/{r['_id']}"
                company_name = r['_source']['company_name']
                text += f"<a href='{agsearch_url}' target='_blank'>{company_name}</a><br />"
        else:
            text += f'\nNo results found.'


def fetch_es_record(url=None):

    bf = CachedBabyFetch()
    url_dict = bf.url_to_key(url)
    domain = url_dict['std_url']

    print('domain  --------------------------->  ', domain)

    elastico = GaiaElastico()
    elastico.connect(alias='opensearch_worker')
    index = ES_INDEX
    query_body = {
        "query": {
            "match": {
                "domain": domain
            }
        }
    }
    res = elastico.es.search(index=index, body=query_body)
    if len(res['hits']['hits']) > 0:
        record = res['hits']['hits'][0]['_source']
        record['index'] = index
    else:
        record = {'error': 'no record found'}
    return record

def es_more_like_this(uuid=None):
    elastico = GaiaElastico()
    elastico.connect(alias='opensearch_worker')
    index = ES_INDEX
    limit = 50

    like_ids = [uuid]

    field_names = ['babyspider.parse.plaintext^0.25', 'webtext.text^0.25', 'short_description^2',
                   'description', ]  # ' short_description^'+wght_med]


    res, query_json = elastico.docs_query_morelike(
        index,
        field_names,
        ids=like_ids,
        query_string='',
        # dislike_ids=dislike_ids,
        pp_from=0,
        pp_count=limit,
        # more_mode=more_mode,
        sorting=[],
        filter_kwargs=None
    )

    names = [r for r in res['hits']['hits'] if r['_id'] != uuid]
    return names[:10]


def fetch_url_and_parse_with_cache(url=None):
    bf = CachedBabyFetch()
    res = bf.fetch_url_or_get_cached(url=url)

    #  get html from result using protocol as key
    protocol = 'https'
    if protocol not in res.get('results', {}).keys():
        protocol = 'http'
    html = res['results'][protocol].get('content', {}).get('html')

    if html is None:
        return 'no content'

    #  set cache
    if not res['cache_hit']:
        bf.set_cache(url=url, data=res['results'][protocol])

    #  parse result
    pt = PlainText()
    text = pt.parse(html=html)
    return text


def list_all_models():
    print(f'available models...')
    model_list = openai.Model.list()['data']
    model_ids = [x['id'] for x in model_list]
    model_ids.sort()
    for m in model_ids:
        print(m)
    print('\n\n...\n\n')


def parse_pdf(pdf_file=None):
    with open(pdf_file, "rb") as f:
        viewer = SimplePDFViewer(f)

        plain_text = ""
        for canvas in viewer:
            plain_text += "".join(canvas.strings)
    import pdb; pdb.set_trace()
    print('plaintext', plain_text)
    return plain_text


def parse_pdf2(pdf_file):
    # creating a pdf reader object
    reader = PdfReader(pdf_file)

    # getting a specific page from the pdf file
    text = ''
    for page in reader.pages:
        text += page.extract_text()

    return text
