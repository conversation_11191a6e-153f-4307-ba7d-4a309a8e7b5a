import json
import streamlit as st
import pandas as pd
from st_aggrid import AgGrid
from st_aggrid import GridOptionsBuilder, JsCode
from gaia.multisearch_api import GaiaMultiSearch
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.djangaia.gaiachat.langchain_adaptor import LangChainAdapator as GaiaChat


def flatten(records=[]):
    res = []
    for row in records:
        new_row = {}
        for key, val in row.items():
            if isinstance(val, dict):
                new_row[key] = json.dumps(val)
            elif isinstance(val, list):
                new_row[key] = [json.dumps(v) for v in val]
            else:
                new_row = row
        res.append(new_row)
    return res


def trim(records=[], to_trim=[]):
    res = []
    for row in records:
        new_row = {}
        for key, value in row.items():
            if key not in to_trim:
                new_row[key] = value
        res.append(new_row)
    return res



def annotate_records(res=None):
    records = []
    if len(res['hits']['hits']) > 0:
        for rec in res['hits']['hits']:
            record = rec['_source']
            record['idx'] = rec['_index']
            record['agsearch'] = f"https://agbase.agfunder.com/search/{rec['_index']}/{rec['_id']}"
            records.append(record)
    else:
        pass
    return records


def filter_records(records=None):
    keys = ['uuid', 'company_name', 'short_description', 'description', 'agsearch', 'langdetect']
    dealer_keys = [ 'incl_agrifood_tech', 'incl_agrifood', 'edu_rank_times1', 'edu_rank_empirical1', 'edu_rank_nature1',
                    'edu_rank_geo_country_empirical1', 'edu_rank_geo_subregion_empirical1',
                    'edu_rank_geo_region_empirical1']

    filtered_records = []
    for rec in records:
        filtered_rec = {}
        for key, val in rec.items():
            if key == 'dealer' and rec['dealer'] is not None:
                for k in dealer_keys:
                    filtered_rec[k] = rec['dealer'][k]
            if key in keys:
                import json
                if isinstance(val, dict):
                    filtered_rec[key] = json.dumps(val)
                else:
                    filtered_rec[key] = val
        filtered_records.append(filtered_rec)
    return filtered_records


def main():
    st.set_page_config(layout="wide")
    st.title("👓 GaiaSearch: Intern")

    with st.sidebar:
        st.title("GaiaIntern")
        query = st.text_input("Search text")
        search_type_google = st.multiselect('Select a Google Service', ('web', 'scholar', 'news', 'patents'))
        query_agsearch = st.checkbox(label='Query AgSearch')
        prompt = st.text_area(label='GaiaChat Prompt')

    google_res_dict = None
    agsearch_records = None
    gaiachat_output = None
    limit = 100

    #  EXTERNAL: GOOGLE MULTI-SEARCH
    if query and search_type_google:
        gms = GaiaMultiSearch()

        #  TODO: check cache layer
        google_res_dict = gms.multisearch(query=query, sources=search_type_google)


    #  EXTERNAL: GAIACHAT
    if query and prompt:
        prompt = prompt.replace('{text}', query)
        gaiachat = GaiaChat(temperature=0)
        _, gaiachat_output = gaiachat.chat_request(text=prompt)


    #  INTERNAL: AGSEARCH
    if query and query_agsearch:
        #  search agsearch
        elastico = GaiaElastico()
        elastico.connect(alias='opensearch_worker_fast')
        ES_INDEX = 'idx_agsearch_worker_fast2'
        wght_high = str(3)
        field_names = [
            'summary_text^' + wght_high
        ]
        res, query = elastico.docs_query(
            index=ES_INDEX,
            field_names=field_names,
            query_string=query,
            filter_kwargs={},
            sorting={},
            pp_from=0,
            pp_count=limit
        )
        agsearch_records = annotate_records(res)
        agsearch_records = filter_records(agsearch_records)


    #  coerce naming
    internal_docs = agsearch_records
    external_docs = google_res_dict


    #  RESULTS
    if external_docs:
        for src in search_type_google:
            if src not in external_docs.keys():
                continue
            to_trim = ['displayed_link', 'favicon', 'snippet_highlight_words', 
                'cached_page_link', 'thumbnail', 'result_id']
            records = external_docs[src]['organic_results']
            records = flatten(records)
            records = trim(records, to_trim)

            st.title(f'{src} results')
            st.write(f'{len(records)} records')

            df = pd.DataFrame.from_records(records)
            gb = GridOptionsBuilder.from_dataframe(df)
            gb.configure_pagination(paginationAutoPageSize=False, paginationPageSize=25)

            gb.configure_column(
                "link", "link",
                cellRenderer=JsCode("""
                    class UrlCellRenderer {
                      init(params) {
                      console.log(params);
                        this.eGui = document.createElement('a');
                        this.eGui.innerText = params.value;
                        this.eGui.setAttribute('href', params.value);
                        this.eGui.setAttribute('style', "text-decoration:none");
                        this.eGui.setAttribute('target', "_blank");
                      }
                      getGui() {
                        return this.eGui;
                      }
                    }
                """)
            )

            gb_grid_options = gb.build()
            AgGrid(df, gridOptions=gb_grid_options, allow_unsafe_jscode=True)


    if internal_docs:
        st.title(f'AgSearch results')
        st.write(f'{len(internal_docs)} records')

        df = pd.DataFrame.from_records(internal_docs)
        gb = GridOptionsBuilder.from_dataframe(df)
        gb.configure_pagination(paginationAutoPageSize=False, paginationPageSize=25)
        gb.configure_column(
                "agsearch", "agsearch",
                cellRenderer=JsCode("""
                    class UrlCellRenderer {
                      init(params) {
                      console.log(params);
                        this.eGui = document.createElement('a');
                        this.eGui.innerText = 'AgSearch Direct Link';
                        this.eGui.setAttribute('href', params.value);
                        this.eGui.setAttribute('style', "text-decoration:none");
                        this.eGui.setAttribute('target', "_blank");
                      }
                      getGui() {
                        return this.eGui;
                      }
                    }
                """)
            )
        gb_grid_options = gb.build()
        AgGrid(df, gridOptions=gb_grid_options, allow_unsafe_jscode=True)

    if gaiachat_output:
        st.title('GaiaChat Results')
        st.write(gaiachat_output)

    if not query:
        st.write('Use the sidebar to Query...')


if __name__ == '__main__':
    main()
