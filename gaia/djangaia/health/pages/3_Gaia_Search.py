import streamlit as st
from st_aggrid import AgGrid
from st_aggrid import GridOptionsBuilder, JsCode
from streamlit import session_state as state
import pandas as pd
import uuid
import sys
import time
import json
sys.path.append("../..")
from gaia.babyspider.cached_babyfetch import CachedBabyFetch
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico


cluster_map = {
    'agsearch_slow': {
        'cluster_alias': 'opensearch_worker',
        'index_alias': 'agsearch_live',
    },
    'AgSearch': {
        'cluster_alias': 'opensearch_worker_giant',
        #'index_alias': 'idx_agsearch_worker_fast1'
        'index_alias': 'agsearch_cb'
    },
    'AgSearchOmni': {
        'cluster_alias': 'opensearch_worker_giant',
        #'index_alias': 'idx_agsearch_worker_fast1'
        'index_alias': 'deep_tech_001'
    },
    #'agsearch_fast_concat': {
    #    'cluster_alias': 'opensearch_worker_fast',
    #    'index_alias': 'idx_agsearch_worker_fast2'
    #},
    'AgFunder Subscribers': {
        'cluster_alias': 'opensearch_worker_giant',
        'index_alias': 'agfunder_subscribers'
    },
    'Omni Decscribe Summary': {
        'cluster_alias': 'opensearch_worker_fast',
        'index_alias': 'idx_omni3__gaiadescribesummary__gpt35turbo1106_db'
    }
}


def is_valid_id(value):
    try:
        uuid.UUID(str(value))
        return True
    except ValueError:
        pass
    if value.startswith('G0__'):
        return True
    return False


#def record_search_latency(query=None, latency=None, csv_filename=None):
#    import csv
#    import datetime
#    current_time = datetime.datetime.now()
#    time_format = "%Y-%m-%d %H:%M:%S"
#    current_time_string = current_time.strftime(time_format)
#    with open(csv_filename, 'a') as csvfile:
#        csv_writer = csv.writer(csvfile)
#        csv_writer.writerow([current_time_string, str(query), latency])

def annotate_records(res=None):
    records = []
    if len(res['hits']['hits']) > 0:
        for rec in res['hits']['hits']:
            record = rec['_source']
            record['idx'] = rec['_index']
            record['agsearch'] = f"https://agbase.agfunder.com/search/{rec['_index']}/{rec['_id']}"
            records.append(record)
    else:
        pass
    return records


def query_elasticsearch(query=None, cluster=None, limit=1000):
    print(f"query elasticsearch ===============================> {query}", cluster)
    cluster_props = cluster_map.get(cluster)
    elastico = GaiaElastico()
    elastico.connect(alias=cluster_props['cluster_alias'])
    try:
        ES_INDEX = list(elastico.es.indices.get(cluster_props['index_alias']))[0]
    except Exception as e:
        raise Exception(f"No index found for alias: {cluster_props['index_alias']}", e)

    #  weights
    wght_high = str(3)
    wght_med = str(2)
    wght_low = str(0.05)

    query_explain = None

    if cluster in ['AgSearch', 'AgSearchOmni']:
        #field_names = [
        #    'summary_text^' + wght_high
        #]
        if ',' in query:
            field_names = ['company_name']
        else:
            field_names = [
                   'company_name',
                   'summary_text^'+wght_med,
                   'summary_text^'+wght_high,
                   'babyspider.parse.plaintext^'+wght_med,
                   'webtext.text^'+wght_low,
                   'alias1^' + wght_low,
                   'cb_url^' + wght_low,
            ]

    elif cluster == 'AgFunder Subscribers':
        field_names = ['email_address^' + wght_high]
        if ',' in query:
            query = '* '.join([q.strip() for q in query.split(',')]) + '*'
        else:
            query = query + '*'
        #field_names = ['email_address^' + wght_high]
    #else:
    #    #  default weights
    #    field_names = [
    #        'company_name^' + wght_high,
    #        'description^' + wght_med,
    #        'short_description^' + wght_med,
    #        'webtext.text^' + wght_high,
    #    ]
    else:
        field_names = ['GOID', 'content.ident.name']


    if is_valid_id(query):
        #raise ValueError('id query')
        res = elastico.get_record_by_id(ES_INDEX, query)
        res = {
            'hits': {
                'hits': res
            }
        }

    elif 'http' in query:
        bf = CachedBabyFetch()
        url_dict = bf.url_to_key(url=query)
        domain = url_dict['std_url']
        query_body = {
            "query": {
                "match": {
                    "domain": domain
                }
            }
        }
        res = elastico.es.search(index=ES_INDEX, body=query_body)
        query_explain = query_body
    elif  ',' in query:
        #raise ValueError('name query')
        res, query_explain = elastico.docs_query_name_search(
                index=ES_INDEX,
                field_names=field_names,
                query_string=query,
                filter_kwargs={},
                sorting={},
                pp_from=0,
                pp_count=limit
            )

    else:
        if 0:
            # TODO: debug knn
            #raise ValueError(len(query_vector))
            res, query_json = elastico.docs_knn(
                index=ES_INDEX,
                vector=query_vector,
                fld='knn',
                k=1
            )
        if 1:
            st.write('regular query')
            #  ACTIVE QUERY
            res, query_explain = elastico.docs_query(
                index=ES_INDEX,
                field_names=field_names,
                query_string=query,
                filter_kwargs={},
                sorting={},
                pp_from=0,
                pp_count=limit
            )
    if query_explain:
        st.write(f'Your Query:\n')
        st.write(query_explain)
        st.write(f'field names: {field_names}')

    if ',' in query:
        limit = len(query.split(','))
    else:
        limit = 0

    if len(res['hits']['hits']) > 0:
        records = []
        for rec in res['hits']['hits']:
            record = rec['_source']
            if cluster == 'AgSearch':
                record['agsearch'] = f"https://agbase.agfunder.com/search/{rec['_index']}/{rec['_id']}"
            records.append(record)
    else:
        return [], ES_INDEX
    return records, ES_INDEX



def es_custom_query(query=None, cluster=None, limit=100):
    cluster_props = cluster_map.get(cluster)
    elastico = GaiaElastico()
    elastico.connect(alias=cluster_props['cluster_alias'])
    try:
        ES_INDEX = list(elastico.es.indices.get(cluster_props['index_alias']))[0]
    except Exception as e:
        raise Exception(f"No index found for alias: {cluster_props['index_alias']}")

    res = elastico.es.search(
        from_=0, size=limit, index=ES_INDEX, body=query)
    records = annotate_records(res)
    if not len(records):
        return None
    return records, ES_INDEX


def es_more_like_this(uuid=None, limit=100):
    elastico = GaiaElastico()
    elastico.connect(alias='opensearch_worker')
    try:
        ES_INDEX = list(elastico.es.indices.get('agsearch_live'))[0]
    except Exception as e:
        raise Exception('No index found for alias: agsearch_live')

    like_ids = [uuid]
    field_names = ['babyspider.parse.plaintext^0.25', 'webtext.text^0.25', 'short_description^2',
                   'description', ]  # ' short_description^'+wght_med]

    res, query_json = elastico.docs_query_morelike(
        ES_INDEX,
        field_names,
        ids=like_ids,
        query_string='',
        # dislike_ids=dislike_ids,
        pp_from=0,
        pp_count=limit,
        # more_mode=more_mode,
        sorting=[],
        filter_kwargs=None
    )

    names = [r for r in res['hits']['hits'] if r['_id'] != uuid]
    return names


def filter_records_cb(records=None):
    keys = ['uuid', 'company_name', 'short_description', 'description', 'agsearch', 'langdetect']
    dealer_keys = [ 'incl_agrifood_tech', 'incl_agrifood', 'edu_rank_times1', 'edu_rank_empirical1', 'edu_rank_nature1',
                    'edu_rank_geo_country_empirical1', 'edu_rank_geo_subregion_empirical1',
                    'edu_rank_geo_region_empirical1']

    filtered_records = []
    for rec in records:
        filtered_rec = {}
        for key, val in rec.items():
            if key == 'dealer' and rec['dealer'] is not None:
                for k in dealer_keys:
                    filtered_rec[k] = rec['dealer'][k]
            if key in keys:
                if isinstance(val, dict):
                    filtered_rec[key] = json.dumps(val)
                else:
                    filtered_rec[key] = val
        filtered_records.append(filtered_rec)
    return filtered_records


def filter_records_subsribers(records=None):
    filtered_records = []
    for rec in records:
        del rec['idx']
        del rec['agsearch']
    return records


def main():
    st.set_page_config(layout="wide")
    st.title("🔍 Gaia Index Search")

    limit = 1000
    records = None

    with st.sidebar:
        CLUSTERS = (
            #'agsearch_slow',
            'AgFunder Subscribers',
            'AgSearch',
            'Omni Decscribe Summary',
            'AgSearchOmni',
            #'agsearch_fast_concat'
        )
        st.title("Query OpenSearch")
        cluster = st.selectbox('Select a Cluster', CLUSTERS)
        input_text = st.text_input("Search (also accepts a comma-sperated list of search terms)", placeholder='Start searching...')
        if 'dev' in st.query_params.keys() or 'dev' in state:
            custom_query = st.text_input("Raw JSON query")
        else:
            custom_query = None

    if input_text:
        records, idx = query_elasticsearch(query=input_text, cluster=cluster, limit=limit)

    elif custom_query:
        records, idx = es_custom_query(custom_query, cluster, limit)
        if records is None:
            records = []

    #  display records as table
    if records is not None and len(records) < 1:
        st.write('No Records Found!')

    elif records is not None and len(records) > 0:
        st.write(f'**{len(records)}** matches found in **{idx}**')
        st.write('You can download the entire result set as a CSV file by right-clicking on any record row.')

        if is_valid_id(input_text) and len(records) == 1:
            st.write(records[0])

        elif len(records) > 0:
            if cluster in ['AgSearch']:
                #  specific to agsearch results
                records = filter_records_cb(records)
            #elif cluster in ['AgFunde Subscribers']:
            #    records = filter_records_subsribers(records)

            df = pd.DataFrame.from_records(records)
            gb = GridOptionsBuilder.from_dataframe(df)
            gb.configure_pagination(paginationAutoPageSize=False, paginationPageSize=200)
            if cluster in ['AgSearch']:
                #  specifc to agsearch results
                gb.configure_column(
                    "agsearch", "agsearch",
                    cellRenderer=JsCode("""
                    class UrlCellRenderer {
                      init(params) {
                      console.log(params);
                        this.eGui = document.createElement('a');
                        this.eGui.innerText = 'AgSearch Direct Link';
                        this.eGui.setAttribute('href', params.value);
                        this.eGui.setAttribute('style', "text-decoration:none");
                        this.eGui.setAttribute('target', "_blank");
                      }
                      getGui() {
                        return this.eGui;
                      }
                    }
                    """)
                )
            else:
                pass
            gb_grid_options = gb.build()
            AgGrid(df, gridOptions=gb_grid_options, allow_unsafe_jscode=True)

    else:
        st.write('Use the sidebar to Query an OpenSearch Index.')
        st.write('Separate multiple query strings using a comma.')

if __name__ == '__main__':
    main()
