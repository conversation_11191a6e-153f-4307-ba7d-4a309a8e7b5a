import streamlit as st
import pandas as pd
import os
import sys
import uuid
import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Protocol

# Add the necessary paths to import CETO components and Agsearch
sys.path.append("../..")
sys.path.append("../../..")
from gaia.gaia_ceto.ceto_v002.chatobj import ChatManager, Conversation, MockLLM, LLMInterface
from gaia.gaia_ceto.ceto_v002.chrono_store import ChronoStore
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from agsearch.services.search_service import SearchService
from agsearch.services.query_service import QueryService
from agsearch.services.result_service import ResultService

# Set up logging
logger = logging.getLogger(__name__)

# LLM Implementations
class OpenAILLM(LLMInterface):
    """OpenAI implementation of the LLM interface."""

    def __init__(self, model_name: str = "gpt-3.5-turbo", temperature: float = 0.7):
        """Initialize the OpenAI LLM.

        Args:
            model_name: The name of the OpenAI model to use.
            temperature: The temperature to use for generation.
        """
        self.model_name = model_name
        self.temperature = temperature

        # Import OpenAI here to avoid dependency issues if not using this implementation
        try:
            import openai
            self.client = openai.OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
        except ImportError:
            logger.error("OpenAI package not installed. Please install it with 'pip install openai'.")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            raise

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the OpenAI API.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the OpenAI API.

        Returns:
            The generated response.
        """
        try:
            # Format the messages for the OpenAI API
            messages = []

            # Add the conversation history
            for msg in context:
                if msg["role"] in ["user", "assistant", "system"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            # Add the current prompt
            messages.append({
                "role": "user",
                "content": prompt
            })

            # Call the OpenAI API
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", 1000)
            )

            # Extract and return the response text
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error generating response from OpenAI: {e}")
            return f"Error generating response: {str(e)}"


class AnthropicLLM(LLMInterface):
    """Anthropic Claude implementation of the LLM interface."""

    def __init__(self, model_name: str = "claude-3-sonnet-20240229", temperature: float = 0.7):
        """Initialize the Anthropic LLM.

        Args:
            model_name: The name of the Anthropic model to use.
            temperature: The temperature to use for generation.
        """
        self.model_name = model_name
        self.temperature = temperature

        # Import Anthropic here to avoid dependency issues if not using this implementation
        try:
            import anthropic
            self.client = anthropic.Anthropic(api_key=os.environ.get("ANTHROPIC_API_KEY"))
        except ImportError:
            logger.error("Anthropic package not installed. Please install it with 'pip install anthropic'.")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic client: {e}")
            raise

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the Anthropic API.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the Anthropic API.

        Returns:
            The generated response.
        """
        try:
            # Format the messages for the Anthropic API
            messages = []

            # Add the conversation history
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    # System messages are handled differently in Anthropic
                    # We'll add them as a system parameter later
                    pass

            # Add the current prompt
            messages.append({
                "role": "user",
                "content": prompt
            })

            # Extract system messages
            system_messages = [msg["content"] for msg in context if msg["role"] == "system"]
            system_prompt = "\n".join(system_messages) if system_messages else None

            # Call the Anthropic API
            response = self.client.messages.create(
                model=self.model_name,
                messages=messages,
                system=system_prompt,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", 1000)
            )

            # Extract and return the response text
            return response.content[0].text

        except Exception as e:
            logger.error(f"Error generating response from Anthropic: {e}")
            return f"Error generating response: {str(e)}"


# Factory function to create LLM instances
def create_llm(llm_type: str = "mock", **kwargs) -> LLMInterface:
    """Create an LLM instance based on the specified type.

    Args:
        llm_type: The type of LLM to create ("mock", "openai", or "anthropic").
        **kwargs: Additional parameters to pass to the LLM constructor.

    Returns:
        An instance of the specified LLM.

    Raises:
        ValueError: If the specified LLM type is not supported.
    """
    if llm_type.lower() == "mock":
        return MockLLM()
    elif llm_type.lower() == "openai":
        return OpenAILLM(**kwargs)
    elif llm_type.lower() == "anthropic":
        return AnthropicLLM(**kwargs)
    else:
        raise ValueError(f"Unsupported LLM type: {llm_type}")


def initialize_chat_manager(llm_type="mock", model_name=None):
    """Initialize the chat manager with the appropriate storage directory and LLM.

    Args:
        llm_type: The type of LLM to use ("mock", "openai", or "anthropic").
        model_name: The specific model name to use (if applicable).

    Returns:
        A ChatManager instance.
    """
    # Use a persistent storage directory
    storage_dir = "/var/lib/gaia/GAIA_FS/ceto_conversations"

    # Ensure the directory exists
    os.makedirs(storage_dir, exist_ok=True)

    # Create the LLM instance with appropriate parameters
    kwargs = {}
    if model_name:
        kwargs["model_name"] = model_name

    # Create the LLM instance
    llm = create_llm(llm_type, **kwargs)

    # Create and return the chat manager
    return ChatManager(storage_dir=storage_dir, llm=llm)


def format_conversation_list(conversations: List[Dict[str, Any]]) -> pd.DataFrame:
    """Format the conversation list for display in a DataFrame."""
    if not conversations:
        return pd.DataFrame(columns=["ID", "Title", "Created", "Messages", "Path"])

    # Extract relevant information for display
    data = []
    for conv in conversations:
        data.append({
            "ID": conv["conversation_id"],
            "Title": conv["title"],
            "Created": conv["created_at"],
            "Messages": conv["message_count"],
            "Path": conv["relative_path"]
        })

    return pd.DataFrame(data)


def display_conversation(conversation: Conversation):
    """Display a conversation in a chat-like interface."""
    if not conversation.messages:
        st.info("This conversation has no messages yet.")
        return

    for msg in conversation.messages:
        role = msg["role"]
        content = msg["content"]

        if role == "user":
            st.markdown(f"**You:** {content}")
        elif role == "assistant":
            st.markdown(f"**Assistant:** {content}")
        elif role == "system":
            st.markdown(f"**System:** {content}")


# Agsearch integration functions
def initialize_search_service():
    """Initialize the search service with the appropriate ES client."""
    try:
        # Connect to OpenSearch
        elastico = GaiaElastico()
        elastico.connect(alias='opensearch_worker_giant')
        
        # Create the search service
        query_service = QueryService()
        result_service = ResultService()
        search_service = SearchService(
            es_client=elastico.es,
            query_service=query_service,
            result_service=result_service
        )
        
        return search_service, elastico
    except Exception as e:
        logger.error(f"Error initializing search service: {e}")
        st.error(f"Error initializing search service: {str(e)}")
        return None, None


def get_available_indices(elastico):
    """Get a list of available indices from OpenSearch."""
    try:
        indices_info = elastico.es.cat.indices(format="json")
        indices = [index['index'] for index in indices_info if not index['index'].startswith('.')]
        return indices
    except Exception as e:
        logger.error(f"Error getting indices: {e}")
        return []


def search_companies(search_service, query, index, page=1, per_page=5):
    """Search for companies using the search service."""
    try:
        # Build search parameters
        params = {"logical": query}
        
        # Execute search
        results, stats = search_service.search_companies(
            params=params,
            page=page,
            per_page=per_page,
            index=index
        )
        
        return results, stats
    except Exception as e:
        logger.error(f"Error searching companies: {e}")
        return [], {"hits": 0}


def format_search_results(results):
    """Format search results for display."""
    if not results:
        return "No results found."
    
    formatted = []
    for i, result in enumerate(results, 1):
        company_name = result.get('name', 'Unnamed Company')
        company_id = result.get('es_id', 'Unknown ID')
        description = result.get('descr', 'No description available.')
        score = result.get('score', 0)
        
        formatted.append(f"**{i}. {company_name}**\n")
        formatted.append(f"ID: {company_id}\n")
        formatted.append(f"Score: {score}\n")
        formatted.append(f"Description: {description[:200]}...\n\n")
    
    return "\n".join(formatted)


def main():
    st.set_page_config(layout="wide", page_title="CETO Chat with Search")

    # Initialize session states
    if "llm_type" not in st.session_state:
        st.session_state.llm_type = "mock"

    if "model_name" not in st.session_state:
        st.session_state.model_name = None

    if "active_conversation" not in st.session_state:
        st.session_state.active_conversation = None
        
    if "search_service" not in st.session_state or "elastico" not in st.session_state:
        st.session_state.search_service, st.session_state.elastico = initialize_search_service()
        
    if "available_indices" not in st.session_state and st.session_state.elastico:
        st.session_state.available_indices = get_available_indices(st.session_state.elastico)
        
    if "selected_index" not in st.session_state and st.session_state.available_indices:
        st.session_state.selected_index = st.session_state.available_indices[0] if st.session_state.available_indices else None

    # Page title and description
    st.title("🤖 CETO Chat with Search")
    st.markdown("""
    CETO is an enhanced chat system with chronological storage for thousands of conversations.
    This version integrates with the Agsearch API to allow searching for companies and other data.
    """)

    # Sidebar for conversation management and settings
    with st.sidebar:
        st.header("Settings")

        # LLM selection
        llm_options = ["mock", "openai", "anthropic"]
        selected_llm = st.selectbox(
            "Select LLM Provider",
            options=llm_options,
            index=llm_options.index(st.session_state.llm_type),
            help="Choose the LLM provider to use for generating responses."
        )

        # Model selection based on LLM provider
        # Import model lists from centralized config
        try:
            from gaia.gaia_llm.model_config import get_anthropic_models, get_openai_models, get_mock_models
            model_options = {
                "mock": get_mock_models(),
                "openai": get_openai_models(),
                "anthropic": get_anthropic_models()
            }
        except ImportError:
            # Fallback if centralized config is not available
            model_options = {
                "mock": ["Default Mock"],
                "openai": ["gpt-4o", "gpt-3.5-turbo"],
                "anthropic": ["claude-3-5-sonnet-20241022", "claude-3-opus-20240229"]
            }

        selected_model = st.selectbox(
            "Select Model",
            options=model_options[selected_llm],
            help="Choose the specific model to use."
        )

        # Only use the model name for real LLMs
        if selected_llm != "mock" and selected_model != "Default Mock":
            model_name = selected_model
        else:
            model_name = None

        # Apply LLM settings button
        if st.button("Apply LLM Settings"):
            # Update session state
            st.session_state.llm_type = selected_llm
            st.session_state.model_name = model_name

            # Reinitialize chat manager with new settings
            st.session_state.chat_manager = initialize_chat_manager(
                llm_type=selected_llm,
                model_name=model_name
            )

            st.success(f"Applied {selected_llm} LLM settings")
            st.rerun()

        # Initialize chat manager if it doesn't exist
        if "chat_manager" not in st.session_state:
            st.session_state.chat_manager = initialize_chat_manager(
                llm_type=st.session_state.llm_type,
                model_name=st.session_state.model_name
            )

        st.header("Conversation Management")

        # Create a new conversation
        st.subheader("Create New Conversation")
        new_title = st.text_input("Conversation Title",
                                 value=f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if st.button("Create New Conversation"):
            # Create a new conversation
            conversation = st.session_state.chat_manager.create_conversation(
                title=new_title,
                user_id=os.getenv("USER", "streamlit_user")
            )

            # Add a system message to start
            system_message = """
            I am an AI assistant with access to company search capabilities. 
            I can help you find information about companies using the Agsearch API.
            
            To search for companies, you can ask me questions like:
            - "Search for companies in the AI sector"
            - "Find companies working on renewable energy"
            - "Look up information about Microsoft"
            
            I'll do my best to help you find the information you need!
            """
            st.session_state.chat_manager.add_message("system", system_message)

            # Save the conversation
            st.session_state.chat_manager.save_conversation()

            # Set as active conversation
            st.session_state.active_conversation = conversation

            st.success(f"Created new conversation: {conversation.title}")
            st.rerun()

        # List existing conversations
        st.subheader("Existing Conversations")

        # Get the list of conversations
        conversations = st.session_state.chat_manager.list_conversations()

        if not conversations:
            st.info("No conversations found. Create a new one to get started.")
        else:
            # Format the conversations for display
            df = format_conversation_list(conversations)

            # Display the conversations in a selectbox
            selected_id = st.selectbox(
                "Select a conversation",
                options=df["ID"].tolist(),
                format_func=lambda x: f"{df[df['ID'] == x]['Title'].iloc[0]} ({x[:8]}...)"
            )

            if st.button("Load Selected Conversation"):
                # Load the selected conversation
                conversation = st.session_state.chat_manager.load_conversation(selected_id)

                if conversation:
                    st.session_state.active_conversation = conversation
                    st.success(f"Loaded conversation: {conversation.title}")
                    st.rerun()
                else:
                    st.error("Failed to load conversation.")

        # Display conversation statistics
        if st.checkbox("Show Statistics"):
            stats = st.session_state.chat_manager.get_conversation_stats()
            st.write(f"Total conversations: {stats['total_conversations']}")

            st.write("By Year/Month:")
            for year, year_data in stats["years"].items():
                st.write(f"  {year}: {year_data['total']} conversations")
                
        # Search settings
        st.header("Search Settings")
        
        if st.session_state.available_indices:
            selected_index = st.selectbox(
                "Select Index",
                options=st.session_state.available_indices,
                index=st.session_state.available_indices.index(st.session_state.selected_index) if st.session_state.selected_index in st.session_state.available_indices else 0,
                help="Choose the index to search in."
            )
            
            if selected_index != st.session_state.selected_index:
                st.session_state.selected_index = selected_index
        else:
            st.warning("No indices available. Please check your OpenSearch connection.")

    # Main chat interface
    if st.session_state.active_conversation:
        conversation = st.session_state.active_conversation

        # Display conversation info
        st.subheader(f"Conversation: {conversation.title}")
        st.write(f"ID: {conversation.conversation_id}")
        st.write(f"Created: {conversation.created_at}")

        # Display the conversation
        st.subheader("Messages")
        display_conversation(conversation)

        # Create two columns for input and search
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Input for new messages
            st.subheader("New Message")
            user_input = st.text_area("Type your message here", height=100)
            
            # Add a status indicator for the current LLM
            st.info(f"Using {st.session_state.llm_type} LLM" +
                    (f" with {st.session_state.model_name}" if st.session_state.model_name else ""))
            
            if st.button("Send"):
                if user_input.strip():
                    with st.spinner("Generating response..."):
                        try:
                            # Check if this is a search request
                            is_search_request = any(keyword in user_input.lower() for keyword in 
                                                  ["search", "find", "look up", "lookup", "query"])
                            
                            if is_search_request and st.session_state.search_service and st.session_state.selected_index:
                                # Extract the search query from the user input
                                # This is a simple approach - in a real system, you might use NLP to extract the query
                                search_query = user_input.lower()
                                for prefix in ["search for", "find", "look up", "lookup", "search", "query"]:
                                    if search_query.startswith(prefix):
                                        search_query = search_query[len(prefix):].strip()
                                
                                # Add the user message
                                st.session_state.chat_manager.add_message("user", user_input)
                                
                                # Perform the search
                                results, stats = search_companies(
                                    st.session_state.search_service,
                                    search_query,
                                    st.session_state.selected_index
                                )
                                
                                # Format the results
                                if results:
                                    formatted_results = format_search_results(results)
                                    response = f"I found {stats['hits']} results for '{search_query}':\n\n{formatted_results}"
                                else:
                                    response = f"I couldn't find any results for '{search_query}'. Please try a different query."
                                
                                # Add the assistant response
                                st.session_state.chat_manager.add_message("assistant", response)
                            else:
                                # Process the message normally
                                _ = st.session_state.chat_manager.process_message(user_input)
                            
                            # Save the conversation
                            st.session_state.chat_manager.save_conversation()
                            
                            # Show success message
                            st.success("Message sent and response received!")
                            
                            # Refresh the UI to show the new message
                            st.rerun()
                        except Exception as e:
                            st.error(f"Error processing message: {str(e)}")
        
        with col2:
            # Direct search interface
            st.subheader("Quick Search")
            search_query = st.text_input("Search Query")
            
            if st.button("Search"):
                if search_query.strip() and st.session_state.search_service and st.session_state.selected_index:
                    with st.spinner("Searching..."):
                        try:
                            # Perform the search
                            results, stats = search_companies(
                                st.session_state.search_service,
                                search_query,
                                st.session_state.selected_index,
                                per_page=10
                            )
                            
                            # Display the results
                            st.write(f"Found {stats['hits']} results")
                            
                            if results:
                                for i, result in enumerate(results, 1):
                                    with st.expander(f"{i}. {result.get('name', 'Unnamed Company')}"):
                                        st.write(f"**ID:** {result.get('es_id', 'Unknown')}")
                                        st.write(f"**Score:** {result.get('score', 0)}")
                                        st.write(f"**Description:** {result.get('descr', 'No description available.')}")
                                        
                                        # Add a button to add this result to the conversation
                                        if st.button(f"Add to Conversation", key=f"add_{i}"):
                                            # Format the result as a message
                                            result_message = f"""
                                            **Search Result: {result.get('name', 'Unnamed Company')}**
                                            
                                            ID: {result.get('es_id', 'Unknown')}
                                            Score: {result.get('score', 0)}
                                            Description: {result.get('descr', 'No description available.')}
                                            """
                                            
                                            # Add the result to the conversation
                                            st.session_state.chat_manager.add_message("user", f"Show me information about {result.get('name', 'this company')}")
                                            st.session_state.chat_manager.add_message("assistant", result_message)
                                            
                                            # Save the conversation
                                            st.session_state.chat_manager.save_conversation()
                                            
                                            st.success("Added to conversation!")
                                            st.rerun()
                            else:
                                st.info("No results found.")
                        except Exception as e:
                            st.error(f"Error searching: {str(e)}")
    else:
        st.info("No active conversation. Create a new one or load an existing one from the sidebar.")


if __name__ == "__main__":
    main()
