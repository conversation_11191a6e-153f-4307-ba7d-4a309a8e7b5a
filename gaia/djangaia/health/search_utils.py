import sys
sys.path.append("../..")

import streamlit as st
import uuid
import json
import numpy as np
from gaia.babyspider.cached_babyfetch import CachedBabyFetch
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.gaia_vec.embed import embed_gen


#  weights
wght_high = str(3)
wght_med = str(2)
wght_low = str(0.05)
field_names = [
    'plaintext^' + wght_high,
    'keywords^' + wght_high,
    'llm_description^' + wght_high,
    'description^' + wght_med,
    'name^' + wght_low,
]


cluster_map = {
    'agsearch_slow': {
        'cluster_alias': 'opensearch_worker',
        'index_alias': 'agsearch_live',
    },
    'agsearch': {
        'cluster_alias': 'opensearch_worker_fast',
        'index_alias': 'idx_omni3_omnisearch1', #'idx_omni3_testfull', #'idx_omni3_test2',
        #'index_alias': 'agsearch_cb'
    },
    #'agsearch_fast_concat': {
    #    'cluster_alias': 'opensearch_worker_fast',
    #    'index_alias': 'idx_agsearch_worker_fast2'
    #},
    'AgFunder Subscribers': {
        'cluster_alias': 'opensearch_worker_fast',
        'index_alias': 'idx_agfunder_subscribers1'
    },
    'Omni Decscribe Summary': {
        'cluster_alias': 'opensearch_worker_fast',
        'index_alias': 'idx_omni3__gaiadescribesummary__gpt35turbo1106_db'
    }
}



def connect_opensearch(cluster=None):
    cluster_props = cluster_map.get(cluster)
    elastico = GaiaElastico()
    elastico.connect(alias=cluster_props['cluster_alias'])
    try:
        ES_INDEX = list(elastico.es.indices.get(cluster_props['index_alias']))[0]
    except Exception as e:
        raise Exception(f"No index found for alias: {cluster_props['index_alias']}", e)
    return elastico, ES_INDEX

def query_elasticsearch(query=None, cluster=None, limit=1000):
    elastico, ES_INDEX = connect_opensearch(cluster=cluster)

    #  weights
    wght_high = str(3)
    wght_med = str(2)
    wght_low = str(0.05)

    if cluster == 'agsearch':

        if 1:
            field_names = [
                'plaintext^' + wght_high,
                'keywords^' + wght_high,
                'llm_description^' + wght_high,
                'description^' + wght_med,
                'name^' + wght_low,
            ]

        if 0:
            field_names = [
                'llm_description^' + wght_high,
                'description^' + wght_med,
                'name^' + wght_low,
            ]

    else:
        field_names = ['GOID', 'content.ident.name']

    if is_valid_id(query):
        res = elastico.get_record_by_id(ES_INDEX, query)
        res = {
            'hits': {
                'hits': res
            }
        }

    elif 'http' in query:
        bf = CachedBabyFetch()
        url_dict = bf.url_to_key(url=query)
        domain = url_dict['std_url']
        query_body = {
            "query": {
                "match": {
                    "domain": domain
                }
            }
        }
        res = elastico.es.search(index=ES_INDEX, body=query_body)

    else:
        if 0:
            # TODO: debug knn
            #raise ValueError(len(query_vector))
            res, query_json = elastico.docs_knn(
                index=ES_INDEX,
                vector=query_vector,
                fld='knn',
                k=1
            )
        if 1:
            #  ACTIVE QUERY
            res, query = elastico.docs_query(
                index=ES_INDEX,
                field_names=field_names,
                query_string=query,
                filter_kwargs={},
                sorting={},
                pp_from=0,
                pp_count=limit
            )
            print('QUERY: ', query)

    if len(res['hits']['hits']) > 0:
        records = []
        for rec in res['hits']['hits']:
            record = rec['_source']
            if cluster == 'AgSearch':
                goid = {rec['_id']}
                #record['agsearch'] = '<a href="https://agbase.agfunder.com/search/agsearch_cb/' + str(goid) + '">AgSearch</a>'
            records.append(record)
    else:
        return [], ES_INDEX
    return records, query



def es_custom_query(query=None, cluster=None, limit=100):
    elastico, ES_INDEX = connect_opensearch(cluster=cluster)

    res = elastico.es.search(
        from_=0, size=limit, index=ES_INDEX, body=query)
    records = annotate_records(res)
    if not len(records):
        return None
    return records, ES_INDEX


def knn_query(query_text=None, elastico=None, cluster=None, idx=None, limit=100):
    if not elastico:
        elastico, ES_INDEX = connect_opensearch(cluster=cluster)
    else:
        ES_INDEX = idx

    embeddings_data = embed_gen([query_text],embed_family="voyage",embed_submodel_key="",mode="query")

    tv = None
    for vector in embeddings_data['embed']:
        if tv is None:
            tv = vector
        else:
            tv_temp = [tv[ii] + vector[ii] for ii in range(0, len(vector))]
            tv = tv_temp

    # mean of vectors
    tv_mean = [tv[ii] / len(vector) for ii in range(0, len(vector))]

    # or get vector by aggregating vectors from the positive set
    knn_name = 'knn'
    knn_size = 1024

    print('///////////////////////////////////////////// KNN QUERY: ', knn_name, knn_size)
    res, query_json = elastico.docs_knn(
        index=ES_INDEX,
        vector=tv_mean,
        fld=knn_name,
        k=limit, pp_count=limit
    )
    print('query done!')
    records = []
    for r in res['hits']['hits']:
        rec = r['_source']
        records.append(rec)

    return records, query_json

def es_more_like_this(idx=None, like_ids=None, elastico=None, cluster=None, query_string=None, limit=25):
    if not elastico:
        elastico, ES_INDEX = connect_opensearch(cluster=cluster)
    else:
        ES_INDEX = idx

    field_names = [
        'plaintext',
        'keywords',
        'llm_description',
        'description',
        'short_description',
        'name',
    ]

    res, query_json = elastico.docs_query_morelike(
        ES_INDEX,
        field_names,
        ids=like_ids,
        query_string=query_string,
        # dislike_ids=dislike_ids,
        pp_from=0,
        pp_count=limit,
        # more_mode=more_mode,
        sorting=[],
        filter_kwargs=None
    )

    print('done query!!!', query_json)
    records = []
    for r in res['hits']['hits']:
        rec = r['_source']
        records.append(rec)

    return records, query_json


def is_valid_id(value):
    if value.startswith('G0__'):
        return True
    return False

def annotate_records(res=None):
    records = []
    if len(res['hits']['hits']) > 0:
        for rec in res['hits']['hits']:
            record = rec['_source']
            record['idx'] = rec['_index']
            #record['agsearch'] = f"https://agbase.agfunder.com/search/{rec['_index']}/{rec['_id']}"
            records.append(record)
    else:
        pass
    return records





def filter_records_cb(records=None):
    keys = ['uuid', 'company_name', 'short_description', 'description', 'agsearch', 'langdetect']
    dealer_keys = [ 'incl_agrifood_tech', 'incl_agrifood', 'edu_rank_times1', 'edu_rank_empirical1', 'edu_rank_nature1',
                    'edu_rank_geo_country_empirical1', 'edu_rank_geo_subregion_empirical1',
                    'edu_rank_geo_region_empirical1']

    filtered_records = []
    for rec in records:
        filtered_rec = {}
        for key, val in rec.items():
            if key == 'dealer' and rec['dealer'] is not None:
                for k in dealer_keys:
                    filtered_rec[k] = rec['dealer'][k]
            if key in keys:
                if isinstance(val, dict):
                    filtered_rec[key] = json.dumps(val)
                else:
                    filtered_rec[key] = val
        filtered_records.append(filtered_rec)
    return filtered_records


def filter_records_omni(records=None):
    filtered_records = []
    for rec in records:
        filtered_rec = {}
        for key, val in rec.items():
            if isinstance(val, dict):
                filtered_rec[key] = json.dumps(val)
            else:
                filtered_rec[key] = val
        filtered_records.append(filtered_rec)
    return filtered_records

def filter_records_subsribers(records=None):
    filtered_records = []
    for rec in records:
        del rec['idx']
        del rec['agsearch']
    return records




