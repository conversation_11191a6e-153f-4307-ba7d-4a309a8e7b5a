import os
import sys
import csv
import click

#  add modules to python path for adjacent importing (python 2.7 way)
parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)

from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.gaia_elasticsearch.es_record_validator import ESRecordValidator
from gaia.builders.omni_builder import OmniBuilder, OmniMultiProcessWorker
from gaia.builders.decorators import timethis
from gaia.builders.utils import es_dummy_generator


def csv_generator(file_path, delimiter=','):
    with open(file_path, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile, delimiter=delimiter)
        for row in reader:
            yield row


def batch_generator(src, batch_size=100):
    batch = []
    for item in src:
        batch.append(item)
        if len(batch) == batch_size:
            yield batch
            batch = []
    if batch:
        yield batch



@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-f', '--full_update', is_flag=True, default=False)
@click.option('-m', '--multiprocess', is_flag=True, default=False)
@click.option('-idx', '--idx', type=str, default=None)
#@timethis
def build_omni(batch_amt=250, hard_limit=None, full_update=False, idx=None, multiprocess=False):
    print('starting omni pipeline...')

    CLUSTER_ALIAS = 'opensearch_worker'
    elastico = GaiaElastico()
    elastico.connect(alias=CLUSTER_ALIAS)

    #  list of augmenters
    augmenters = [cls for cls in cache_for]


    #  create a builder instance
    omni_builder = OmniBuilder(elastico_conn=elastico, idx='idx_omni_temp')
    omni_builder.augmenters = augmenters

    #  get source iterator
    src_path = '/home/<USER>/agbase_admin_min37_dj200/DATA/cb_dnld_20230721/organizations.csv'
    print(f'get src generator... {src_path}')
    src = csv_generator(file_path=src_path)


    if augment_records:
        callback = augment_and_cache
    elif insert_es:
        omni_builder = OmniBuilder(elastico_conn=elastico, idx='idx_omni_temp')
        omni_builder.augmenters = augmenters
        omni_builder._before_insert()
        callback = omni_builder.es_from_cache


    if multiprocess:
        N = multiprocessing.cpu_count() - 1 or 1
        queue = multiprocessing.Queue()

        for i in range(N):
            if es_insert:
                CLUSTER_ALIAS = 'opensearch_worker'
                elastico = GaiaElastico()
                elastico.connect(alias=CLUSTER_ALIAS)
                omni_builder = OmniBuilder(elastico_conn=elastico, idx='idx_omni_temp')
                omni_builder.augmenters = augmenters
                omni_builder._before_insert()
                callback = omni_builder.es_from_cache

            #  create workers
            worker = OmniMultiProcessWorker(id='worker {i+1}')

            #  workers which process items from queue
            worker_process = multiprocessing.Process(
                target=worker.run,
                args=(i+1, queue, callback))
            #insert_worker.daemon = True

            #  start all workers
            insert_worker.start()

            #  keep reference to all workers
            workers.append(insert_worker)

    else:
        #  single process
        for batch in batch_generator(src=csv_gen, batch_size=batch_amt):
            augment_and_cache(batch)

            if es_insert:
                omni_builder = OmniBuilder(elastico_conn=elastico, idx='idx_omni_temp')
                omni_builder.augmenters = augmenters
                omni_builder._before_insert()
                omni_builder.es_from_cache(batch)


    if es_insert:
        omni_builder._after_insert()

if __name__ == '__main__':
    build_omni()
