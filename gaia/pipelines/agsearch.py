#  usage: python -m gaia.pipelines.agsearch -b 100 -l 1000

import os
import sys
import click

#  add modules to python path for adjacent importing (python 2.7 way)
parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.gaia_elasticsearch.es_record_validator import ESRecordValidator
from gaia.builders.builders import AgsearchBuilder
from gaia.builders.utils import es_dummy_generator
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_fs.paths import gfs_folder_local


CACHE_PATH = gfs_folder_local(tree='gfs_datasets')



def get_idx_latest_dest(elastico=None):
    indices = ['idx_agsearch_worker_fast1', 'idx_agsearch_worker_fast2']
    ACTIVE_IDX = list(elastico.es.indices.get('agsearch_cb'))[0]
    OTHER_IDX = indices.remove(ACTIVE_IDX)
    OTHER_IDX = indices[0]
    dict = {'active_idx': ACTIVE_IDX , 'other_idx': OTHER_IDX}
    print(f'get_idx_latest_dest {dict}')
    return dict



def generator_from_src(src, batch_amt):
    if file_path.endswith('.csv'):
        reader = pd.read_csv(file_path, chunksize=batch_amt)
    elif file_path.endswith('.parquet'):
        reader = pd.read_parquet(file_path, chunksize=batch_amt)
    else:
        raise ValueError("File format not supported. Please provide a CSV or Parquet file.")

    for chunk in reader:
        yield chunk


@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-f', '--full_update', is_flag=True, default=False)
@click.option('-m', '--multiprocess', is_flag=True, default=False)
@click.option('-idx', '--idx', type=str, default=None)
@click.option('-src', '--src', type=str, default=None)
def build_agsearch(batch_amt=250, hard_limit=None, full_update=False, idx=None, multiprocess=False, src=None):
    #  src is an es index
    SRC_CLUSTER_ALIAS = 'opensearch_worker_fast'
    SRC_IDX = 'idx_cb10_fresh1'
    elastico = GaiaElastico()

    #  destination cluster, idx
    DEST_CLUSTER_ALIAS = 'opensearch_worker_fast'
    elastico = GaiaElastico()
    elastico.connect(alias=DEST_CLUSTER_ALIAS)
    res = get_idx_latest_dest(elastico=elastico)
    #  dest idx can be overridden
    DEST_IDX = idx if idx else res['other_idx']
    print(f'DEST_IDX: {DEST_IDX}')

    #  [OPTIONAL] file source
    if src:
        src_kv = None
        src_gen = generator_from_src(src, batch_amt)

    #  [DEFAULT] CB10 KV source
    else:
        src_gen = None
        file_path = os.path.join(CACHE_PATH, 'cb10.db')
        src_kv = KeyValueStore_sqlite3(file_path=file_path)
        src_kv.cursor.execute(f"SELECT * FROM main_table")


    #  DEFAULT BEHAVIOUR
    if full_update:
        print('FULL UPDATE...')
        #  FULL SCAN of ES returns a generator
        #elastico.connect(alias=SRC_CLUSTER_ALIAS)
        #src_gen, _ = elastico.docs_scan(index=SRC_IDX, pp_count=batch_amt)

        #  run builder
        #elastico.connect(alias=DEST_CLUSTER_ALIAS)
        builder = AgsearchBuilder(
           dest_idx=DEST_IDX,
           es_alias=DEST_CLUSTER_ALIAS
        )
        builder.run(
            iterable=src_gen,
            multiprocess=multiprocess,
            batch_amt=batch_amt,
            HARD_LIMIT=hard_limit,
            full_update=True,
            src_kv=src_kv
        )


    #  NOT USED CURRENTLY
    elif not full_update:
        print('PARTIAL UPDATE...')
        #  get most recent date in agsearch
        LATEST_IDX = res['latest_idx']
        latest = elastico.latest_record(index=LATEST_IDX)

        pp_from = 0
        size = batch_amt
        #  debug
        seen = {}

        while True:
            print('===========================> pp_from ', pp_from)
            #cb_res = elastico.after_date(
            #    index=SRC_IDX,
            #    date=latest['date'],
            #    pp_from=pp_from,
            #    pp_count=size
            #)

            # we will skip rows until status log id
            elastico.connect(alias=SRC_CLUSTER_ALIAS)
            cb_res, _ = elastico.docs_scan(
                index=SRC_IDX,
                pp_count=batch_amt
            )

            agsearch_builder = AgsearchBuilder(
                dest_idx=DEST_IDX,
                es_alias=DEST_CLUSTER_ALIAS
            )
            agsearch_builder.run(
                iterable=cb_res,
                multiprocess=multiprocess,
                batch_amt=batch_amt,
                HARD_LIMIT=hard_limit,
                full_update=False
            )

            if results_size < size:
                print('END OF RUN')
                break

            print('incerement pp_from')
            pp_from += size

        print('done!')


def validate(uuid='12082774-e32f-43f4-9a9f-1a2cb14569bc'):
    """
    Validate a single ES Record as a sanity check.
    """
    print(f'validating record { uuid }...')
    elastico = GaiaElastico()
    elastico.connect(alias='opensearch_worker_fast')
    record = elastico.get_record_by_id(
        index=DEST_IDX,
        uuid=uuid
    )
    #  validate record
    validator = ESRecordValidator()
    return validator.validate(record=record[0])

if __name__ == '__main__':
    build_agsearch()
    is_valid = validate(DEST_IDX)
    print('================> idx is valid: ', is_valid)
