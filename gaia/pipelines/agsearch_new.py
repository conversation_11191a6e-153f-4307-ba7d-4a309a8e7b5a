#  usage: python -m gaia.pipelines.agsearch -b 100 -l 1000

import os
import sys
import click

#  add modules to python path for adjacent importing (python 2.7 way)
parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)

from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.gaia_elasticsearch.es_record_validator import ESRecordValidator
from gaia.builders.builders import AgsearchBuilder
from gaia.builders.decorators import timethis
from gaia.builders.utils import es_dummy_generator

'''
#  notes
#  put alias on index
elastico.es.indices.put_alias(index='idx_cb10_fresh1', name='cb10')
#  get by alias
idx = list(elastico.es.indices.get('cb10'))[0]
#  delete alias
elastico.es.indices.delete_alias(index='idx_cb10_fresh1', name='cb10')
'''

#  debug
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3


def get_destination_index_name(elastico=None):
    print('get destination...')
    most_stale = ()
    for idx in ('idx_agsearch_worker2', 'idx_agsearch_worker3'):
        if elastico.es.indices.exists(index=idx):
            #  get most recent date
            latest = elastico.latest_record(index=idx)
            print(f'latest record for {idx}: {latest}')
            if not most_stale or latest['date'] > most_stale[1]:
                most_stale = (idx, latest['date'])
        else:
            #  return this index to build if not exists
            return idx
    return most_stale[0]


def get_idx_latest_dest(elastico=None):
    indexes = ['idx_agsearch_worker1', 'idx_agsearch_worker2']
    LATEST_INDEX = list(elastico.es.indices.get('agsearch_live'))[0]
    DEST_IDX = indexes.remove(LATEST_INDEX)
    DEST_IDX = indexes[0]
    dict = {'latest_idx': LATEST_INDEX , 'dest_idx': DEST_IDX}
    print(f'get_idx_latest_dest {dict}')
    return dict

@click.command()
@click.option('-b', '--batch_amt', type=int, default=250)
@click.option('-l', '--hard_limit', type=int, default=None)
@click.option('-f', '--full_update', is_flag=True, default=False)
@click.option('-idx', '--idx', type=str, default=None)
def build_agsearch(batch_amt=250, hard_limit=None, full_update=False, idx=None):
    print('agsearch pipeline...')

    #  decide on a dest index, other is the latest idx
    CLUSTER_ALIAS = 'opensearch_worker'
    elastico = GaiaElastico()
    elastico.connect(alias=CLUSTER_ALIAS)

    # source index is a constant
    SRC_IDX = 'idx_cb10_fresh1'

    #  get most recent index
    res = get_idx_latest_dest(elastico=elastico)
    LATEST_IDX = res['latest_idx']

    #  get dstination index
    DEST_IDX = idx if idx else res['dest_idx']

    print(f'SRC_IDX: {SRC_IDX}, DEST_IDX: {DEST_IDX}')
    #  create a builder instance (not ES specific)
    agsearch_builder = AgsearchBuilder(
        dest_idx=DEST_IDX,
        es_alias=CLUSTER_ALIAS
    )

    if not full_update:
        print('PARTIAL UPDATE...')
        #  get most recent date in agsearch
        latest = elastico.latest_record(index=LATEST_IDX)

        pp_from = 0
        batch_size = batch_amt

        while True:
            print(f'pp_from: {pp_from}')

            # we will skip rows until status log id
            cb_res, _ = elastico.docs_scan(
                index=SRC_IDX,
                pp_count=batch_amt
            )

            agsearch_builder.run(
                iterable=cb_res,
                multiprocess=True,
                batch_amt=batch_amt,
                HARD_LIMIT=hard_limit,
                full_update=False
            )

            if results_size < batch_size:
                print('END OF RUN')
                break

            print('incerement pp_from')
            pp_from += batch_size

        print('PARTIAL UPDATE done!')

    if full_update:
        print('FULL UPDATE...')
        #  FULL SCAN
        #  get es results
        cb_res, _ = elastico.docs_scan(
            index=SRC_IDX,
            pp_count=batch_amt
        )

        #  TODO: use mget to get all the unicode records

        agsearch_builder.run(
            iterable=cb_res,
            multiprocess=True,
            batch_amt=batch_amt,
            HARD_LIMIT=hard_limit,
            full_update=True,
            es_conn=elastico
        )

    #  TODO: set live index?
    print('set live index not implemented here...')


    def validate(uuid='12082774-e32f-43f4-9a9f-1a2cb14569bc'):
        """
        Validate a single ES Record as a sanity check.
        """
        print(f'validating record { uuid }...')
        elastico = GaiaElastico()
        elastico.connect(alias='opensearch_worker')
        record = elastico.get_record_by_id(
           index=DEST_IDX,
           uuid=uuid
        )
        #  validate record
        validator = ESRecordValidator()
        return validator.validate(record=record[0])



if __name__ == '__main__':
    build_agsearch()
    is_valid = validate()
    print('================> idx is valid: ', is_valid)
