from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.pipelines.agsearch import get_idx_latest_dest


def set_live_idx(elastico=None):
    elastico = GaiaElastico()
    elastico.connect(alias='opensearch_worker_fast')

    res = get_idx_latest_dest(elastico=elastico)
    ACTIVE_IDX = res['active_idx']
    OTHER_IDX = res['other_idx']

    idx_alias = 'agsearch_cb'

    count_active = int(elastico.record_count(index=ACTIVE_IDX)[ACTIVE_IDX]['count'])
    count_other = int(elastico.record_count(index=OTHER_IDX)[OTHER_IDX]['count'])

    print(f'count other [{OTHER_IDX}]: {count_other}')
    print(f'count active [{ACTIVE_IDX}]: {count_active}')

    if count_other > count_active:
        print(f'setting index alias...{OTHER_IDX}')
        print(f'unsetting index alias...{ACTIVE_IDX}')

        #  set/ remove alias
        elastico.es.indices.put_alias(index=OTHER_IDX, name=idx_alias)
        elastico.es.indices.delete_alias(index=ACTIVE_IDX, name=idx_alias)

    return True


if __name__ == '__main__':
    set_live_idx()
