from gaia.babyspider.babyfetch import <PERSON><PERSON><PERSON><PERSON>
from gaia.gaia_clerks.clerk_collate import Clerk_Collate
from gaia.gaia_clerks.clerk_describe import Clerk_Describe
from gaia.gaia_clerks.clerk_sdg import Clerk_SDG
from gaia.gaia_llm.gaia_llm import LiteLLMJsonChatCompletionClient
from gaia.gaia_clerks import clerks


class Chief<PERSON><PERSON><PERSON>(clerks.Clerk):

    """Chief Clerk can wrangle the specialized clerks."""

    def __init__(self):
        self.cheap_llm = LiteLLMJsonChatCompletionClient(model='gpt-3.5-turbo-1106')
        self.expensive_llm = LiteLLMJsonChatCompletionClient(model='gpt-4-1106-preview')
        self.collate_clerk = Clerk_Collate(client=self.cheap_llm)
        self.sdg_clerk = Clerk_SDG(client=self.expenseive_llm)
        self.describe_clerk = Clerk_Describe(client=self.expensive_llm)

    def collate_and_describe(self, text: str):
        collated_text = self.collate_clerk.collate(text=text)
        self.describe_clerk.parse(text=collated_text)

    def collate_and_sdg(self, text: str):
        collated_text = self.collate_clerk.collate(text=text)
        self.sdg_clerk.goals(text=collated_text)
