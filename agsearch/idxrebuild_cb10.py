# usage: python -m agsearch.idxrebuild_cb10

import os
import sys
import click
import shutil

#  add modules to python path for adjacent importing (python 2.7 way)
parent = os.path.abspath('./agsearch/')
sys.path.insert(0, parent)


from agsearch.cb_query import cbdb_get_conn, cbdb_fetch_from
from aflib.elastic import esgen
from pprint import pprint as pp
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.util.keyvalue.keyvalue import KeyValueStore_sqlite3
from gaia.gaia_fs.paths import gfs_folder_local


'''
- loop through all cb_org_all records
- write them into elasticsearch using existing format
- consider adding rounds?
'''


def gendata(docarray=None, index_name=None):
    """
    Generates ES documents for insertion from array.
    """

    for doc in docarray:
        new_doc = {}
        for k, v in doc.items():
            # remove empty keys
            if doc[k] == '' or doc[k] is None:
                #del doc[f]
                pass
            else:
               new_doc[k] = v
        from datetime import datetime
        if new_doc.get('founded_on'):
            try:
                new_doc['founded_on'] =  datetime.strptime(new_doc['founded_on'], '%Y-%m-%d')
            except Exception as e:
                new_doc['founded_on'] =  datetime.strptime(new_doc['founded_on'].split(' ')[0], '%Y-%m-%d')
        #new_doc['created_at'] = datetime.strptime(new_doc['created_at'], '%Y-%m-%dT%H:%M:%S')
        #new_doc['created_at'] = new_doc['created_at'].strftime("%Y-%m-%d %H:%M:%S")
        yield {
            "_id": new_doc["uuid"],
            "_index": index_name,
            "_type": index_name,
            "_source": new_doc
        }

@click.command()
@click.option('-idx', '--index', type=str, default='')
@click.option('-r', '--recreate', is_flag=True, default=True)
def insert_data(index=None, recreate=True):
    """Insert data into index."""

    #  TODO
    recreate = False

    IDX_CB = index

    print('index: ', IDX_CB)

    #  connect to Opensearch
    #elastico = GaiaElastico()
    #elastico.connect(alias='opensearch_worker_fast')

    #  connect to psql
    conn = cbdb_get_conn()
    print('psql conn', conn)

    #  re-create ES index, if required
    if recreate:
        print('RECREATING INDEX...')
        #elastico.index_delete(index=IDX_CB)
        #elastico.index_create(index=IDX_CB)


    #  loop params
    off = 0
    amt = 5000
    ok = True
    max_uuid = None
    hard_limit = None


    #  get destination kv
    CACHE_PATH = gfs_folder_local(tree='gfs_datasets')
    BUILD_CACHE_PATH = os.path.join(CACHE_PATH, 'build')
    dest_kv_path = os.path.join(BUILD_CACHE_PATH, 'cb10.db')
    dest_kv = KeyValueStore_sqlite3(file_path=dest_kv_path)
    print(f'resetting kv: {dest_kv.file_path}')
    dest_kv.reset()
    #dest_kv.set_mode('safe_tortoise')

    while ok:
        #  get batch of rows from postgres agbase1
        print('max_uuid',max_uuid)
        res = cbdb_fetch_from(conn, max_uuid=max_uuid, limit=amt,)
        max_uuid = res[-1]['uuid']
        if len(res) == amt:
            ok = True
        else:
            print('break', [len(res), amt])
            ok = False


        #  debugging
        if hard_limit and off >= hard_limit:
            ok = False


        #  insert batch of PG records into ES
        #print('insert batch into Opensearch...')
        data = gendata(docarray=res, index_name=IDX_CB)
        #elastico.insert_bulk(data, multi=True)

        #  insert into KV
        print('insert batch into KV store...')
        for row in data:
            dest_kv.set(row['_id'], row)

        off += amt
        print("LOOP ", off)
        if off % 20 == 2:
            print(off, amt)

        #  verify insertion
        #res2 = ec.doc_get(IDX_CB, res[0]['uuid'])
        #print('cb10 verifying...')
        #print('cb10 success...')
        #pp(res2)
    #dest_kv.commit()
    dest_kv.close()

    #  move tro /datasets
    #  count rows (fast method)
    old_kv_path = os.path.join(CACHE_PATH, 'cb10.db')
    old_kv = KeyValueStore_sqlite3(file_path=old_kv_path)

    print(f'kv counts: old: {old_kv.count_fastupperlim()}, dest: {dest_kv.count_fastupperlim()}')
    if dest_kv.count_fastupperlim() >= old_kv.count_fastupperlim():
        print('/////////////////////////////////////////////// moving to dest path...', dest_kv_path)
        shutil.move(dest_kv_path, old_kv_path)
    else:
        print('/////////////////////////////////////////////// src_path is smaller than dest_path so will not move!', [old_kv_path, dest_kv_path])


if __name__ == '__main__':
    try:
        insert_data()
    except Exception as e:
        print(e)
        raise
        #rebuild_index(idx=IDX_CB)
        #insert_data()

    print('done!')
