# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from datetime import datetime
import requests
import collections

from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import HttpResponse
from urlparse import urlparse
from slugify import slugify

from json2html import json2html
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.contrib.auth.decorators import login_required
from django.http import Http404

from pdb import set_trace as st

import dealer
import dealer.views
from .legacy_views import txt_length_limiter
from .metrics import Metrics
from ext_airtable.atcb_update_companies_table import *

import logging
from lib_agbase import *
from agbase.models import Org
from pprint import pprint as pp
import numpy as np
import scipy

logger = logging.getLogger('django')

PP_COUNT = 100


from esconn import *
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico

#ES_ALIAS = 'aws_east'
ES_ALIAS = 'opensearch_worker'
#ES_ALIAS = 'aws_west'

def get_field_names(title_wght=str(3)):
    wght_high = str(3)
    wght_med = str(2)
    wght_low = str(0.05)

    field_names = [
        'cb_url^'+wght_med,
        'alias1^'+wght_med,
        'company_name^'+title_wght,
        'description^'+wght_med,
        'short_description^'+wght_med,
        'babyspider.parse.plaintext^'+wght_med,
        'webtext.text^'+wght_low,
    ]
    #field_names = [ 'short_description','description', ]
    return field_names


def get_tags(uuids=None):
    print('--------------------------------------------------- start get tags')
    #  get all tags in db for uuids list
    tags_dict = {}
    ratings = AsRating.objects\
        .filter(es_id__in=uuids, rating=1)\
        .select_related('context')\
        .exclude(context__context_type='junk')

    for rating in ratings:
        dd = {'id': rating.context.id, 'text': rating.context.name}
        if rating.es_id not in tags_dict.keys():
            tags_dict[rating.es_id] = []
        tags_dict[rating.es_id].append(dd)
    print('--------------------------------------------------- end get tags')
    return tags_dict


def get_pred_tags(rec, context_dict):
    pred_tags = []
    tag_threshold = 0.5

    for tag, val in rec['mlorg_text']['tag'].items():
        if val > tag_threshold:
            try:
                cid = context_dict['TAG__' + tag]
                pred_tags.append({'tag': tag, 'val': round(val, 2), 'id': cid, 'error': False})
            except KeyError:
                pred_tags.append({'tag': tag + ' ERROR', 'val': round(val, 2), 'id': None, 'error': True})
    #  sort by val
    pred_tags = sorted(pred_tags, key=lambda d: d['val'], reverse=True)
    return pred_tags


def get_contexts_as_dict(contexts=None):
    if not contexts:
        contexts = AsContext.objects.all()
    dd = {c.name: c.pk for c in contexts}
    return dd

##############
# ES QUERIES #
##############
def search_get_es_detail(request, query, user):
    """
    Get Company Instance.
    """
    elastico = GaiaElastico()
    elastico.connect(alias=ES_ALIAS)

    if query is None or query == {}:
        return render(request, 'agbase/snippets/search_es.html')

    #  ES query: fetch single record from index
    index = query['idx']
    q_body_id = {
        "terms": {
            "_id": [query['id']]
        }
    }
    sq = {"query": q_body_id}

    res = None
    pp_from = 0
    pp_count = PP_COUNT

    # get ES query results
    #  TODO: shouldn't this use ec.doc_get() ?
    try:
        # directly accesing the elasticsearch api
        res = elastico.es.search(index=index, from_=pp_from, size=pp_count, body=sq)
    except elasticsearch.ElasticsearchException as e:
        print("ES Search failed:", e)
        pass
    except Exception as e:
        print("ES Search: UNKNOWN EXCEPTION", e)
        pass
    else:
        # no exception
        print("ES Search success")
        pass

    # extract results
    try:
        r = res['hits']['hits'][0]
    except IndexError:
        raise Http404("Org does not exist")



    rec = r['_source']

    # becomes es_type, es_id...
    copyover = ['_type', '_id', '_index']
    for c in copyover:
        rec['es'+c] = r[c]

    rec['score'] = int(float(r['_score'])*10)

    rec2 = {}

    # build rec2 using some filters to improve representation
    links = dict()
    for k in rec:
        # dont inclue log.* fields in update
        if k.startswith('log.') or k.startswith('logo') or k.startswith('webtext') or k.startswith('knn') or k.startswith('metrics'):
            continue
        val = rec[k]
        if (type(val) == str or type(val) == unicode) and 'http' in val:
            o = urlparse(val)
            print(o)
            if o.scheme in ['http', 'https']:
                if k == "url_logo":
                    pass
                else:
                    pass
                    links[k + '__link'] = '''
                    <a target="_blank" href="%s">%s</a>
                    ''' % (val, val)
        rec2[k] = rec[k]

    webtext = []
    # if 'webtext' in rec.keys() and '_source' in rec['webtext'] and 'docs' in rec['webtext']['_source']:
    #    webtext = rec['webtext']['_source']['docs']
    if 'webtext' in rec.keys():
        webtext = rec['webtext']



    import agbase.power_bar

    #rec2['mlorg_text_tbl'] =  agbase.power_bar.dict_tableizer( rec2['mlorg_text'] )

    spacer = "<br><br>"


    tag = rec2['mlorg_text']['tag']
    tag = collections.OrderedDict( sorted( tag.items() ) )
    llist = [ [k, tag[k]] for k in tag.keys() ]
    rec2['mlorg_text_tag'] =  agbase.power_bar.table_probabilities( llist, min=0.1, maxcount=25,  )+spacer

    cat = rec2['mlorg_text']['cat']
    cat = collections.OrderedDict( sorted( cat.items() ) )
    llist = [ [k, cat[k]] for k in cat.keys() ]
    rec2['mlorg_text_cat'] =  agbase.power_bar.table_probabilities( llist, min=0.1, maxcount=25,  )+spacer

    top = rec2['mlorg_text']['top']
    top = collections.OrderedDict( sorted( top.items() ) )
    llist = [ [k, top[k]] for k in top.keys() ]
    rec2['mlorg_text_top'] =  agbase.power_bar.table_probabilities( llist, min=0.1, maxcount=25,  )+spacer

    xform = rec2['xform_preds']
    llist = [ ('incl_flag', xform['incl_flag'] ), ('incl_strict', xform['incl_strict'] )]
    rec2['ml_xform_top'] =  agbase.power_bar.table_probabilities( llist, min=0.0, maxcount=25,  )+spacer

    xform = rec2['xform_preds']['cat_probs']
    xform = collections.OrderedDict( sorted( xform.items() ) )
    llist = [ [k, xform[k]] for k in xform.keys() ]
    rec2['ml_xform_cat'] =  agbase.power_bar.table_probabilities( llist, min=0.0, maxcount=25,  )+spacer

    rec2['name'] = rec2['company_name']

    rec2['descr'] = ""

    if 'description' in rec2:
        rec2['descr'] = rec2['description']
    rec2['short_descr'] = ""

    if 'short_description' in rec2:
        rec2['short_descr'] = rec2['short_description']

    rec2['url_logo'] = rec.get('logo_url')


    dels = [] #[ 'mlorg_text', 'agbase', 'Metrics', ]  # 'xform_pred',
    for d in dels:
        if d in rec2:
            del rec2[d]

    #  dealer
    pretty_data = {}
    if rec2.get('dealer'):
        pretty_data['dealer'] = rec2['dealer']
        del rec2['dealer']

    #  investors
    if rec2.get('investors'):
        pretty_data['investors'] = rec2['investors']
        del rec2['investors']

    #  rounds
    if rec2.get('rounds'):
        pretty_data['rounds'] = rec2['rounds']
        del rec2['rounds']

    #  people
    if rec2.get('people'):
        pretty_data['people'] = rec2['people']
        del rec2['people']


    #  babyspider
    if rec2.get('babyspider'):
        pretty_data['babyspider'] = rec2['babyspider']
        del rec2['babyspider']

    if rec2.get('plaintext'):
        pretty_data['plaintext'] = rec2['plaintext']
        del rec2['plaintext']

    rec_ord = collections.OrderedDict( sorted(rec2.items()) )

    return render(
        request, 'agbase/snippets/search_es_detail.html',
        {'comp': rec_ord, 'webtext': webtext, 'pretty_data': pretty_data}
    )


def get_es_10_list(query=None, index=None, user=None, filter_kwargs=None, sorting=None, page=1, pp_count=50,
                   es_conn=None, contexts=None, show_tagging=False):
    """
    Query Elasticsearch, return list of matches.
    """
    pp_from = (pp_count * page - pp_count)

    print('++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ GET LIST')

    #  we cannot exceed 10k results,
    #  so never return > 10k -> last page can be less than pp_count
    if pp_from + pp_count > 10000:
        pp_count = 10000 - pp_from

    field_names = get_field_names()

    if 'logical_negative' not in query:
        query['logical_negative'] = ''

    print('********* pp_from: {}'.format(pp_from))

    if query['logical_negative']:
        res, query_json = es_conn.docs_query_dual(
            index,
            field_names,
            query_string_positive=query['logical'],
            query_string_negative=query['logical_negative'],
            pp_from=pp_from,
            pp_count=pp_count,
            filter_kwargs=filter_kwargs,
            sorting=sorting
        )
    else:
        res, query_json = es_conn.docs_query(
            index,
            field_names,
            query_string=query['logical'],
            pp_from=pp_from,
            pp_count=pp_count,
            filter_kwargs=filter_kwargs,
            sorting=sorting
        )
    #  get "industry critic" model predictions about the query
    preds = {}
    if res and 'hits' in res and 'hits' in res['hits']:
        stats = {'hits': res['hits']['total']}
    else:
        stats = {'hits': 0}

    #  collect all uuids for results
    uuids = []
    for r in res['hits']['hits']:
        uuids.append(r['_id'])

    if show_tagging:
        #  get all tags with uuids
        tags_dict = get_tags(uuids=uuids)
        #  get all contexts
        context_dict = get_contexts_as_dict(contexts)

    #  collect matches
    match = []
    for r in res['hits']['hits']:

        rec = r['_source']

        # becomes es_type, es_id...
        copyover = ['_type', '_id', '_index']
        for c in copyover:
            rec['es'+c] = r[c]

        fields_to_shorten = ['name', 'short_descr', 'descr']

        for k in rec.keys():
            if k in fields_to_shorten:
                rec[k] = txt_length_limiter(rec[k])

        rec['score'] = None
        if r['_score']:
            rec['score'] = int(float(r['_score']) * 10)

        #  data transforms
        metrics = Metrics(obj=rec)
        rec['metrics'] = metrics.compute_metrics()

        if show_tagging:
            #  add explicit tags
            rec['explicit_tags'] = tags_dict.get(rec['uuid'])
            # add predicted tags
            rec['pred_tags'] = get_pred_tags(rec, context_dict)

        match.append(rec)

    return preds, stats, match, query_json


def get_es_10_knn(query_vector, index, knn_name, knn_size, user=None, filter_kwargs=None, sorting=None, page=1, pp_count=200, es_conn=None):
    """
    Query Elasticsearch, return list of matches.
    """
    pp_from = (pp_count * page - pp_count)

    #  we cannot exceed 10k results,
    #  so never return > 10k -> last page can be less than pp_count
    if pp_from + pp_count > 10000:
        pp_count = 10000 - pp_from

    print('********* pp_from: {}'.format(pp_from))


    if 1:
        res, query_json = es_conn.docs_knn(
            index,
            vector=query_vector,
            fld=knn_name,
            k = pp_count
            #pp_from=pp_from,
            #pp_count=pp_count,
            #filter_kwargs=filter_kwargs,
            #sorting=sorting
        )


    qv_np = np.array( query_vector)

    preds = {}
    if res and 'hits' in res and 'hits' in res['hits']:
        stats = {'hits': res['hits']['total']}
    else:
        stats = {'hits': 0}

    #  collect matches
    match = []
    for r in res['hits']['hits']:
        rec = r['_source']
        #pp(rec['knn'])
        knn_np = np.array( rec['knn'] )
        #print(knn_np)
        dist2 = np.linalg.norm( knn_np - qv_np )
        dist_cos = scipy.spatial.distance.cosine( knn_np, qv_np )
        print("dist2",dist2,"dist_cos",dist_cos)

        #st()
        # becomes es_type, es_id...
        copyover = ['_type', '_id', '_index']
        for c in copyover:
            rec['es'+c] = r[c]

        fields_to_shorten = ['name', 'short_descr', 'descr']

        for k in rec.keys():
            if k in fields_to_shorten:
                rec[k] = txt_length_limiter(rec[k])

        rec['score'] = None
        if r['_score']:
            rec['score'] = int(float(r['_score']) * 10)

        #  data transforms
        metrics = Metrics(obj=rec)
        rec['metrics'] = metrics.compute_metrics()

        match.append(rec)

    return preds, stats, match, query_json


def get_es_10_morelike(like_ids=None, query_string=None, #dislike_ids=None,
                       show_tagging=False, index=None, page=1, pp_count=50, more_mode="LIKE", sorting=None, filter_kwargs=None, es_conn=None):
    """
    More Like This Search.
    """

    pp_from = (pp_count * page - pp_count)

    #field_names = get_field_names(title_wght=str(1))
    wght_low=str(0.05)
    wght_med=str(1)
    #field_names = ['webtext._source.docs.text^'+wght_low,] # 'description^'+wght_med,' short_description^'+wght_med]
    #field_names = ['*',]

    # do not include name
    field_names = ['babyspider.parse.plaintext^0.25','webtext.text^0.25','short_description^2','description', ] #' short_description^'+wght_med]

    #  defaults
    preds = {}
    match = []
    stats = {'hits': 0}

    if not like_ids: # and not dislike_ids:
        query_json = {'error': 'like array is empty.'}
        return preds, stats, match, query_json

    if show_tagging:
        #  get all tags with uuids
        tags_dict = get_tags(uuids=like_ids)
        #  get all contexts
        context_dict = get_contexts_as_dict()

    res, query_json = es_conn.docs_query_morelike(
        index,
        field_names,
        ids=like_ids,
        query_string=query_string,
        # dislike_ids=dislike_ids,
        pp_from=pp_from,
        pp_count=pp_count,
        #more_mode=more_mode,
        sorting=sorting,
        filter_kwargs=filter_kwargs
    )

    #  get "industry critic" model predictions about the query
    preds = {}
    if res and 'hits' in res and 'hits' in res['hits']:
        stats = {'hits': res['hits']['total']}
    else:
        stats = {'hits': 0}

    #  collect matches
    match = []
    for r in res['hits']['hits']:
        rec = r['_source']

        # becomes es_type, es_id...
        copyover = ['_type', '_id', '_index']
        for c in copyover:
            rec['es'+c] = r[c]

        fields_to_shorten = ['name', 'short_descr', 'descr']

        for k in rec.keys():
            if k in fields_to_shorten:
                rec[k] = txt_length_limiter(rec[k])

        rec['score'] = None
        if r['_score']:
            rec['score'] = int(float(r['_score'])*1.0)

        #  data transforms
        metrics = Metrics(obj=rec)
        rec['metrics'] = metrics.compute_metrics()

        if show_tagging:
            #  add explicit tags
            rec['explicit_tags'] = tags_dict.get(rec['uuid'])
            # add predicted tags
            rec['pred_tags'] = get_pred_tags(rec, context_dict)

        match.append(rec)

    return preds, stats, match, query_json


def get_es_batch(index=None, ids=None, sorting=None, page=1, pp_count=50, filter_kwargs=None, es_conn=None,
                 show_tagging=False):
    """
    Get multiple docs from ES by id.
    """

    pp_from = (pp_count * page - pp_count)

    res, query_json = es_conn.docs_mget(index=index, ids=ids, sorting=sorting, filter_kwargs=filter_kwargs, pp_from=pp_from, pp_count=pp_count)

    #  get "industry critic" model predictions about the query
    preds = {}
    if res and 'hits' in res and 'hits' in res['hits']:
        stats = {'hits': res['hits']['total']}
    else:
        stats = {'hits': 0}

    if show_tagging:
        #  get all tags with uuids
        tags_dict = get_tags(uuids=ids)
        #  get all contexts
        context_dict = get_contexts_as_dict()

    #  collect matches
    match = []
    for r in res['hits']['hits']:
        rec = r['_source']

        # becomes es_type, es_id...
        copyover = ['_type', '_id', '_index']
        for c in copyover:
            rec['es' + c] = r[c]

        fields_to_shorten = ['name', 'short_descr', 'descr']

        for k in rec.keys():
            if k in fields_to_shorten:
                rec[k] = txt_length_limiter(rec[k])

        #  data transforms
        metrics = Metrics(obj=rec)
        rec['metrics'] = metrics.compute_metrics()

        if show_tagging:
            #  add explicit tags
            rec['explicit_tags'] = tags_dict.get(rec['uuid'])

            # add predicted tags
            rec['pred_tags'] = get_pred_tags(rec, context_dict)

        match.append(rec)

    return preds, stats, match, query_json


#################
#  Helper Utils #
#################
def search_es_dnld(df):
    '''
    view to download pandas DataFrame as csv
    '''
    #df = df.drop(['agbase','dealer','investors','metrics','rounds','webtext'], axis=1)
    df = df.drop(['agbase','investors','metrics','rounds','webtext'], axis=1)
    csv = df.to_csv()
    response = HttpResponse(csv, content_type='text/csv')
    fn_slug = slugify(unicode(datetime.datetime.now().strftime("%Y-%m-%d %H:%M")))
    response['Content-Disposition'] = 'attachment; filename=agsearch-dnld-%s.csv' % fn_slug
    return response



def filter_by_rating(match, filter, context_id):
    """
    Filter results by AsRating.
    """
    as_ratings = AsRating.objects.filter(context_id=context_id)
    if filter == 'pos':
        as_ratings = as_ratings.filter(rating=1)
    elif filter == 'neg':
        as_ratings = as_ratings.filter(rating=-1)
    else:
        return match
    # filtered = [row for row in match if row['es_id'] in as_ratings.values_list('es_id', flat=True)]
    filtered = [row for row in match]
    return filtered


def get_query_history(context_id=None, limit=40):
    """
    Return Query History.
    """
    as_q = AsQuery.objects.all().order_by('-created_at')
    if context_id:
        as_q = as_q.filter(ascontext__id=context_id)
    as_queries = []
    _ = []
    for item in as_q:
        query = {
            'pk': item.pk,
            'logical': item.logical_string,
            'context_id': context_id
        }
        if query['logical'] not in _:
            as_queries.append(query)
            _.append(query['logical'])
        else:
            #  TODO: remove duplicates
            item.delete()
    return as_queries[:limit]


def update_query_history(as_query=None, context_id=None, query=None, user=None, count=None):
    if as_query:
        as_query.created_by = user
        # TODO: solve for this bug?
        # https://code.djangoproject.com/ticket/27538
        as_query.query_json = dict(query)
        as_query.created_at = datetime.datetime.now()
        as_query.save()
    #  else save new query if doesn't already exist
    else:
        as_queries = AsQuery.objects.filter(ascontext_id=context_id, query_json=json.dumps(query))
        if len(as_queries) > 0:
            #  update the 'created_by' date of first instance when saved
            as_query = as_queries[0]
        else:
            as_query = AsQuery(ascontext_id=context_id, created_by=user, result_count=count)
            as_query.query_json = dict(query)
        as_query.save()


def annotate_es_results(match=None):
    """Add extra context to ES Companies results."""

    #  load airtable
    at_cburls = atcb_cache_fetch_cburls()
    # Round 1: annotate with additional data ---

    # Convert paginated queryset to python list before rendering.
    # So we can easily augment the records using python data.
    array_results = []
    uuids = []
    for item in match:
        #print('item',i,item)

        # append fields to item:
        ## airtable status

        item['in_airtable'] = False
        if 'cb_url' in item and item['cb_url']:
            clean_cb_url = dealer.views.url_scrub_query_and_fragment(item['cb_url'])
            if clean_cb_url in at_cburls:
                item['in_airtable'] = True

        ## people records
        ## saved model predictions
        ## investor stats?

        #  TODO: this seems redundant,
        #  as a we already have this data in paginated_results
        uuids.append(item['uuid'])
        array_results.append(item)

    # fetch people for all these uuids:
    # select * from django.cb16x_orgspeople_summary_join where company_uuid in [ list of uuids ]
    # has: edu_rank
    # doesnt have: other person data
    # join with:
    # select * from django.cb16x_people_summary where company_uuid in [ list of uuids ] # collect person_uuid

    # annotate results

    # future: paste all this into elasticsearch (?)

    for i, item in enumerate(array_results):
        #array_results.append(item)
        pass

    return array_results


def pagination(match=None, stats=None, page=None):
    """Paginate Company Results."""
    if type(stats['hits']) == dict:
        total = stats['hits']['value']
    else:
        total = stats['hits']
    array_results = annotate_es_results(match=match)

    #  pagination
    #  Paginator just takes an iterable, pp_count is results per page
    paginator = Paginator(range(0, total), PP_COUNT)
    try:
        paginated = paginator.page(page)
    except PageNotAnInteger:
        paginated = paginator.page(1)
    except EmptyPage:
        paginated = paginator.page(paginator.num_pages)

    return paginated


#######################
####  CB 10 views  ####
#######################
@login_required(login_url='/admin/')
def search_view_10_detail(request, es_index=None, company_id=None):
    """
    Instance view.
    """
    print(' ****** Detail View')
    query = {'id': company_id, 'idx': es_index}
    return search_get_es_detail(request, query, request.user)


@login_required(login_url='/admin/')
def search_view_10(request):
    """"
    ES Index: CB_10. View.
    """

    print('+++++++++++++++++++++++++++++++++++++++++++++++++ VIEW:search_view_10')

    more_like_modes = [
        'more-like-boost',
        'more-like-unlike',
        'more-like',
        'more-like-single'
    ]

    metrics = Metrics()


    form_options = {
        'inclusion_options': [
            {'name': '---', 'value': ''},
            {'name': '> 0.95', 'value': '>0.95'},
            {'name': '> 0.9', 'value': '>0.9'},
            {'name': '> 0.8', 'value': '>0.8'},
            {'name': '> 0.7', 'value': '>0.7'},
            {'name': '> 0.5', 'value': '>0.5'},
        ],
        'created_date_options': [
            {'name': '---', 'value': '', 'delta': ''},
            {'name': 'last week', 'value': 1, 'delta': 'weeks'},
            {'name': 'last month', 'value': 1, 'delta': 'months'},
            {'name': 'last 3 months', 'value': 3, 'delta': 'months'},
            {'name': 'last 6 months', 'value': 6, 'delta': 'months'},
            {'name': 'last year', 'value': 1, 'delta': 'years'},
            {'name': 'last 5 years', 'value': 5, 'delta': 'years'},
            {'name': 'last 10 years', 'value': 10, 'delta': 'years'},
        ],
        'founded_date_options': [
            {'name': '---', 'value': '', 'delta': ''},
            {'name': 'last year', 'value': 1, 'delta': 'years'},
            {'name': 'last 5 years', 'value': 5, 'delta': 'years'},
            {'name': 'last 10 years', 'value': 10, 'delta': 'years'},
        ],
        'funded_date_options': [
            {'name': '---', 'value': '', 'delta': ''},
            {'name': 'last year', 'value': 1, 'delta': 'years'},
            {'name': 'last 5 years', 'value': 5, 'delta': 'years'},
            {'name': 'last 10 years', 'value': 10, 'delta': 'years'},
        ],
        'funding_round_options': [
            {'name': '---', 'value': ''},
            {'name': '> 0', 'value': '>0'},
            {'name': '> 1', 'value': '>1'},
            {'name': '> 2', 'value': '>2'},
            {'name': '> 3', 'value': '>3'},
        ],
        'in_airtable_options': [
            {'name': '---', 'value': ''},
            {'name': 'True', 'value': 'true'},
            {'name': 'False', 'value': 'false'},
        ],
        'webtext_options': [
            {'name': '---', 'value': ''},
            {'name': 'True', 'value': '200'},
        ],
        'sort_options': metrics.sort_options()
    }

    #  Defaults
    query = None
    pp_count = PP_COUNT
    max_query_hist_display = 30
    sorting = []


    try:
        #  connect to elasticsearch
        elastico = GaiaElastico()
        elastico.connect(alias=ES_ALIAS)
        ES_INDEX = None
        ES_INDEX = list(elastico.es.indices.get('agsearch_live'))[0]
        if not ES_INDEX:
            raise Exception('No index found for alias: agsearch_live')

        messages.add_message(
            request, messages.INFO, 'Connecting to {} {}'.format(elastico.details, ES_INDEX))

    except Exception as e:
        messages.add_message(
            request, messages.ERROR, 'Elasticsearch connection Failed. {}'.format(e), extra_tags='danger')

    #  GET vars
    params = {
        'idx': ES_INDEX,
        'context_id': None,
        'mode': None,
        'xform_incl_flag': None,
        'xform_incl_strict': None,
        'funding_round': None,
        'created_after': None,
        'founded_after': None,
        'funding_latest': None,
        'in_airtable': None,
        'webtext': None,
        'as_query_id': None,
        'status': None,
        'logical': '',
        'logical_negative': '',
        'sort': None,
        'page': 1,
        'csv': None,
        'show_tagging': None
    }
    as_contexts = AsContext.objects.exclude(context_type='junk').order_by('name')

    #  populate param values
    for key, val in params.items():
        params[key] = request.GET.get(key, val)

    as_ratings = AsRating.objects.filter(context_id=params['context_id'])
    #st()

    #  for CSV download, hardcode limit/ offset
    if params['csv']:
        pp_count = 1000
        page = 1

    #  redirect with GET var if no context
    if not params['context_id']:
        url = request.get_full_path()
        try:
            context = as_contexts.get(name='GENERAL')
        except AsContext.DoesNotExist:
            context = as_contexts.first()
        return redirect('{}?context_id={}'.format(url, context.pk))

    #  default view
    if request.GET.keys() == ['context_id'] or request.GET.keys() == ['context_id', 'show_tagging']:
        print(' ****** DEFAULT View')
        return render(
            request, 'agbase/snippets/search_es10.html', {
                'conn_host': elastico.details.get('endpoint'),
                'conn_port': elastico.details.get('port'),
                'conn_index': params['idx'],
                'ascontexts': as_contexts,
                'as_queries': get_query_history(context_id=params['context_id'], limit=max_query_hist_display),
                'context_id': params['context_id'],
                'form_options': form_options,
            })

    #  list view. GET (search)
    if request.GET:
        print(' ****** List View. {}', request.GET)

        #  wildcard logical if empty string OR mode is neg/positive
        if params['logical'] == '' and not params['mode']:
            logical = '*'
        query = {
            'logical': params['logical'],
            'logical_negative': params['logical_negative'],
            'context_id': params['context_id'],
        }

        #  filters
        filter_kwargs = {}

        #  filter has webtext (agspider)
        if params['webtext']:
            filter_kwargs['webtext.status'] = params['webtext']

        #  TODO: xform.flag and xform.strict
        if params['xform_incl_flag']:
            filter_kwargs['xform_preds.incl_flag'] = params['xform_incl_flag']
        if params['xform_incl_strict']:
            filter_kwargs['xform_preds.incl_strict'] = params['xform_incl_strict']

        #  filter funding round
        if params['funding_round']:
            filter_kwargs['funding_rounds'] = params['funding_round']

        #  filter founded on after
        if params['founded_after']:
            filter_kwargs['founded_on'] = '>' + params['founded_after']

        #  filter created after
        if params['created_after']:
            filter_kwargs['created_at'] = '>' + params['created_after']

        #  filter founded on after
        if params['funding_latest']:
            filter_kwargs['last_funding_on'] = '>' + params['funding_latest']

        if params['status']:
            filter_kwargs['status.keyword'] = '=' + params['status']

        #  filter in airtable
        if params['in_airtable']:
            filter_kwargs['in_airtable'] = '=' + params['in_airtable']

        #  sorting
        if params['sort']:
            k, v = params['sort'].split(',')
            sort_mode = [d['mode'] for d in form_options.get('sort_options') if d['value'] == params['sort']][0]
            sorting = {'key': k, 'value': v, 'mode': sort_mode}

        #  more like this
        if params['mode'] in more_like_modes:
            if params['mode'] in ['more-like-boost', 'more-like-unlike', 'more-like']:

                #  get all ids in context with a positive rating
                like_ids = as_ratings.filter(
                     rating=1).values_list('es_id', flat=True)

                dislike_ids = as_ratings.filter(
                    rating=-1
                ).values_list('es_id', flat=True)

                p='v1 likeids: {} , dislike_ids: {}'.format(like_ids, dislike_ids)
                print(p)
                logger.info(p)
            else:
                like_ids = []
                if request.GET.get('id'):
                    id = request.GET.get('id')
                    like_ids = [id]
                dislike_ids = []

                p='v2 likeids: {} , dislike_ids: {}'.format(like_ids, dislike_ids)
                print(p)
                logger.info(p)


            if params['mode'] == 'more-like-boost':
                more_mode = "BOOST"
            elif params['mode'] == 'more-like-unlike':
                more_mode = "UNLIKE"
            elif params['mode'] == 'more-like-single':
                more_mode = "LIKE"
            elif params['mode'] == 'more-like':
                more_mode = "LIKE"
            else:
                more_mode = "LIKE"
            preds, stats, match, query_json = get_es_10_morelike(
                like_ids=like_ids,
                query_string=params['logical'],
                #dislike_ids=dislike_ids,
                index=params['idx'],
                page=int(params['page']),
                pp_count=pp_count,
                more_mode=more_mode,
                sorting=sorting,
                filter_kwargs=filter_kwargs,
                es_conn=elastico,
                show_tagging=params['show_tagging']
            )

        elif params['mode'] == 'more-like-sem':

            #  get all ids in context with a positive rating
            like_ids = as_ratings.filter(  # es_idx=params['idx'],
                rating=1).values_list('es_id', flat=True)

            query_vector = elastico.average_vector( index=params['idx'], doc_ids=list(like_ids), fld='knn_paraphrase-MiniLM-L6-v2' )

            knn_size = 384
            preds, stats, match, query_json = get_es_10_knn(
                query_vector=query_vector.tolist(),
                index=params['idx'],
                knn_name='knn',
                knn_size=knn_size,
                user=request.user,
                es_conn=elastico
                #    page=int(params['page']),
                #    pp_count=pp_count,
                #    filter_kwargs=filter_kwargs,
                #    sorting=sorting
            )

        #  mode pos/ neg
        elif params['mode'] == 'pos' or params['mode'] == 'neg':
            #st()
            #  TODO: for now ignore logical query
            rating = 1
            if params['mode'] == 'neg':
                rating = -1
            ids = as_ratings.filter(
                rating=rating
            ).values_list('es_id', flat=True)
            #  convert to list
            ids = [id for id in ids]
            #  get a list of documents by id
            preds, stats, match, query_json = get_es_batch(
                index=params['idx'],
                ids=ids,
                page=int(params['page']),
                pp_count=pp_count,
                sorting=sorting,
                filter_kwargs=filter_kwargs,
                es_conn=elastico,
                show_tagging=params['show_tagging']
            )

        elif params['mode'] == 'sem':
            sem_text = params['logical']  # semantic

            # get the vector from input phrase...
            import learn.sbert.sbert_flask_client
            sbert_res = learn.sbert.sbert_flask_client.embeds( [sem_text] ) # [ 1.0, 1.0 ]
            vector = sbert_res['embed'][0]

            # or get vector by aggregating vectors from the positive set
            #

            ##query = agsearch_knn_get_query( vector )
            #query_vector=None, index=None, knn_name, knn_size, user
            knn_name='knn' #_'+learn.sbert.sbert_base.model_tag
            knn_size=learn.sbert.sbert_base.vector_size

            preds, stats, match, query_json = get_es_10_knn(
                query_vector=vector,
                index=params['idx'],
                knn_name='knn',
                knn_size=knn_size,
                user=request.user,
                es_conn=elastico
            #    page=int(params['page']),
            #    pp_count=pp_count,
            #    filter_kwargs=filter_kwargs,
            #    sorting=sorting
            )


        #  'normal' or 'stored' search
        elif params['as_query_id'] or params['mode'] == 'logical':
            print('logical/ stored ***************')

            as_query = None

            #  stored query
            if params['as_query_id']:
                #  TODO: try/ except
                #  overwrite query
                as_query = AsQuery.objects.get(pk=params['as_query_id'])
                query = as_query.query_json
                #  we need to update dict as query_json has a different structure
                query['logical'] = as_query.logical_string
                query['context_id'] = as_query.ascontext.id

            #  new match query
            preds, stats, match, query_json = get_es_10_list(
                query=query,
                index=params['idx'],
                user=request.user,
                page=int(params['page']),
                pp_count=pp_count,
                filter_kwargs=filter_kwargs,
                sorting=sorting,
                es_conn=elastico,
                contexts=as_contexts,
                show_tagging=params['show_tagging']
            )
            #  Update Query History
            update_query_history(
                as_query=as_query,
                context_id=params['context_id'],
                query=query_json,
                user=request.user,
                count=stats['hits']['value']
            )

        #  return CSV
        if params['csv']:
            df = pd.DataFrame.from_dict(match)
            return search_es_dnld(df)

        #  return HTML
        # why does this seem to change between dict and int?
        # 'int' object has no attribute '__getitem__'

        array_results = annotate_es_results(match=match)
        paginated = pagination(match=match, stats=stats, page=int(params['page']))

        query_history = get_query_history(
            context_id=params['context_id'], limit=max_query_hist_display)

        context = {
            'q': query,
            'preds': preds,
            'stats': stats,
            'comps':  array_results,
            'paginated': paginated,
            'context_id': params['context_id'],
            'ascontexts': as_contexts,
            'ratings_dict': agbase_context_ratings(context_id=params['context_id']),
            'as_queries': query_history,
            'query_json':  json2html.convert(json=query_json),
            'form_options': form_options,
            'conn_host': elastico.details.get('endpoint'),
            'conn_port': elastico.details.get('port'),
            'conn_index': params['idx'],
            'show_tagging': params['show_tagging'],
            'all_tags': as_contexts
        }
        return render(
            request, 'agbase/snippets/search_es10.html', context)


def vector_view(request):
    #  Defaults
    #  GET vars
    pos = request.GET.get('pos')
    neg = request.GET.get('neg')

    if neg=="None":
        neg=None

    #res =

    # match = 'ships'
    query = {'pos': pos, 'neg': neg, }
    print(query)

    similarity_results=[(),]
    msgs = None

    if pos:
        response = requests.get('http://127.0.0.1:5000/get_vectors', params=query)
        print(response)
        print( response._content )

        if response.status_code != 200:
            pass
        else:
            res = json.loads(response._content)

            if res['status']!=200:
                msgs = [res['msg']]
            else:
                print("Similarity")
                pp(res['result']['similarity'][:30])

                similarity_results = res['result']['similarity'] # [( 'name',0.34), ( 'name2',0.564),],

    context = {
            'pos': pos,
            'neg': neg,
            'res': similarity_results,
            'messages': msgs,
    }
    return render(
        request, 'agbase/snippets/vector.html', context)
