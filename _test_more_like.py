from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico


ES_INDEX = 'idx_agsearch_worker1'


def es_more_like_this(uuid=None):
    elastico = GaiaElastico()
    elastico.connect(alias='opensearch_worker')
    index = ES_INDEX

    # TODO: get uuid
    like_ids = [uuid]

    field_names = ['babyspider.parse.plaintext^0.25', 'webtext.text^0.25', 'short_description^2',
                   'description', ]  # ' short_description^'+wght_med]


    res, query_json = elastico.docs_query_morelike(
        index,
        field_names,
        ids=like_ids,
        query_string='',
        # dislike_ids=dislike_ids,
        pp_from=0,
        pp_count=10,
        # more_mode=more_mode,
        sorting=[],
        filter_kwargs=None
    )
    import pdb; pdb.set_trace()


if __name__ == '__main__':
    uuid = '370d3275-1bcf-f30d-308a-8456bd328e94'
    es_more_like_this(uuid=uuid)
