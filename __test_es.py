from datetime import datetime, timedelta
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico

def main():
    alias = 'opensearch_worker'
    idx = 'idx_agsearch_worker1'
    elastico = GaiaElastico()
    elastico.connect(alias=alias)
    latest = elastico.latest_record(index=idx)

    print(f"latest: {latest['date']}")
    #ll = latest['date'].replace('2022', '2020')
    print(f"type of latest: {type(latest['date'])}")

    #  go back one day
    ld = latest['date'] - timedelta(days=1)
    print('ago', ld)

    print('---------------------------------------> query...', [alias, idx])
    cb_res = elastico.after_date(index='idx_cb10_newdoc', date=ld, pp_from=0, pp_count=250)
    print(f'len of cb_res: {len(cb_res)}')

    for r in cb_res:
        #pass
        print(f"\n{r['_source']['company_name']}: {r['_source']['created_at']}")


if __name__ == '__main__':
   main()
